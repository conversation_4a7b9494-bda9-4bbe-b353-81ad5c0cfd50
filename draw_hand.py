import matplotlib.pyplot as plt
import numpy as np

# 数据点，格式为 [type, x, y, z]
data_points = [
    [0, -0.11725, -0.31182, -0.42617],
    [1, -0.13496, -0.33337, -0.37541],
    [2, -0.09920, -0.32610, -0.40376],
    [3, -0.07433, -0.32232, -0.42818],
    [4, -0.05514, -0.32433, -0.45909],
    [5, -0.04651, -0.32237, -0.47828],
    [6, -0.11693, -0.32281, -0.40555],
    [7, -0.08421, -0.29422, -0.45889],
    [8, -0.06672, -0.30274, -0.48892],
    [9, -0.05524, -0.31852, -0.50095],
    [10, -0.04755, -0.33412, -0.50522],
    [11, -0.12235, -0.32232, -0.40709],
    [12, -0.10489, -0.29676, -0.46250],
    [13, -0.08563, -0.30915, -0.49618],
    [14, -0.07523, -0.33300, -0.49823],
    [15, -0.07277, -0.35034, -0.48769],
    [16, -0.13194, -0.32791, -0.40904],
    [17, -0.12113, -0.30779, -0.46134],
    [18, -0.10361, -0.32209, -0.49029],
    [19, -0.09056, -0.34344, -0.49121],
    [20, -0.08853, -0.35851, -0.47893],
    [21, -0.14185, -0.33582, -0.40881],
    [22, -0.13889, -0.31822, -0.45917],
    [23, -0.12635, -0.33233, -0.47967],
    [24, -0.11428, -0.34424, -0.48775],
    [25, -0.11275, -0.35792, -0.47880]
]

# 提取坐标和类型
types = [point[0] for point in data_points]
x_coords = [point[1] for point in data_points]
y_coords = [point[2] for point in data_points]
z_coords = [point[3] for point in data_points]

# 创建3D图形
fig = plt.figure(figsize=(10, 8))
ax = fig.add_subplot(111, projection='3d')

# 绘制散点图
scatter = ax.scatter(x_coords, y_coords, z_coords, c=types, cmap='viridis',
s=50)

# 为每个点添加类型标签
for i, (x, y, z, type_val) in enumerate(zip(x_coords, y_coords, z_coords,
types)):
    ax.text(x, y, z, f'{type_val}', color='red', fontsize=8)

# 添加颜色条
cbar = plt.colorbar(scatter, ax=ax, pad=0.1)
cbar.set_label('Joint Type')

# 设置坐标轴标签
ax.set_xlabel('X Axis')
ax.set_ylabel('Y Axis')
ax.set_zlabel('Z Axis')
ax.set_title('3D Visualization of Left Hand Joints (26 points)')

# 调整视角以获得更好的视角
ax.view_init(elev=20, azim=45)

# 显示图形
plt.tight_layout()
plt.show()
