Jan 25 00:00:00 ar_logcat: Run Ar_logcat service.
Jan 25 00:00:00 ar_logcat: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:00 ar_logcat: vin start @ sdk version [5hJk2Ld8Rt9sFpQw] 
Jan 25 00:00:00 ar_logcat: ^[[35;22m[124390384][0297][0x7f86270200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3693 load vin driver start^[[0m
Jan 25 00:00:00 ar_logcat: ^[[31;22m[124390387][0297][0x7f86270200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:00 ar_logcat: ^[[35;22m[124390389][0297][0x7f86270200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3957 load vin driver end max_sensor_count=5^[[0m
Jan 25 00:00:00 ar_logcat: Create socked success
Jan 25 00:00:00 ar_logcat: Bind socket success
Jan 25 00:00:00 ar_logcat: Listen socket success
Jan 25 00:00:00 ar_logcat: client accept thread running
Jan 25 00:00:01 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.982650][0488][0x7f7affd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.983507][0488][0x7f7affd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.984587][0488][0x7f7affd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.984639][0488][0x7f7affd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0297][0x7f86693200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390396][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390397][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390397][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390397][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390397][0297][0x7f855ba200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390397][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 358e7000 35b05000 35b8e000 0 0 0 35c11000 35e2f000 35eb8000 0 0 0 
Jan 25 00:00:01 ar_logcat: aec index 193748247 line 1125 gain 1.000000 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390397][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390397][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390397][0297][0x7f855db200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390398][0297][0x7f855db200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390442][0297][0x7f86693200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ao s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 ar_logcat: ^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390443][0297][0x7f855db200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ai s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 ar_logcat: ^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390449][0297][0x7f84cfa200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390449][0297][0x7f84cfa200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:if/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 ar_logcat: 0x7f84cfa200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390450][0297][0x7f84c95200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390450][0297][0x7f85578200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390450][0297][0x7f85578200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390450][0297][0x7f84cfa200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390450][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390450][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390450][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390450][0297][0x7f84cfa200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 35f41000 3615f000 361e8000 0 0 0 3626b000 36489000 36512000 0 0 0 
Jan 25 00:00:01 ar_logcat: aec index 1622232336 line 1125 gain 1.000000 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390451][0297][0x7f84cfa200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390451][0297][0x7f84cfa200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390451][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 3659b000 367b9000 36842000 0 0 0 368c5000 36ae3000 36b6c000 0 0 0 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390453][0297][0x7f85927200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390454][0297][0x7f85927200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:04 ar_logcat: open the driver sns:imx681 obj:stSnsimx681Obj lib:libsns_imx681.so 
Jan 25 00:00:04 ar_logcat: ^[[31;22m[25 00:00:04.809288][0488][0x7f937fe1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3554 !!!!!!!!!! Note:your app cfg the isp read and vif write  as no compress,this mode isp read and vif write will cost more 
Jan 25 00:00:04 ar_logcat: ^[[31;22m[25 00:00:04.809534][0488][0x7f937fe1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3657 stream id 0, no need cfg the sns_var_info^[[0m
Jan 25 00:00:04 ar_logcat: ^[[35;22m[124390778][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=0^[[0m
Jan 25 00:00:04 ar_logcat: ^[[35;22m[124390791][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:04 ar_logcat: ^[[35;22m[124390791][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390792][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390792][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4384 power on vc 0 0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390795][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4386 power on vc end 0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[33;22m[124390800][0297][0x7f84a87200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:05 ar_logcat: ^[[33;22m[124390801][0297][0x7f84b2c200][VO_CORE][WARN] display_api.c: dc_deinit : 2323 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390801][0297][0x7f84b0b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390801][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390801][0297][0x7f855ba200][SYS_CORE][VI_KEY] mipi_rx_9411.c: mipi_rx_power_up_9411 : 64 enable the mipi cfg and pcs clock, and set pcs to clk=100000000hz^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390801][0297][0x7f855ba200][SYS_CORE][VI_KEY] mipi_rx.c: one_mipi_init : 1204 mipi 4 init done^[[0m
Jan 25 00:00:05 ar_logcat: aec index 1973751614 line 7042 gain 1.000000 
Jan 25 00:00:05 ar_logcat:  imx681_stream_on
Jan 25 00:00:05 ar_logcat:  imx681_trigger_on
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390802][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390802][0297][0x7f855db200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390802][0297][0x7f84b0b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390802][0297][0x7f855db200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390802][0297][0x7f855db200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390802][0297][0x7f855db200][VI_CORE][ERROR] stream.c: link_filter_external : 407 check_linking_stream error:-2146402228^[[0m
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390802][0297][0x7f855db200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390802][0297][0x7f855db200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390802][0297][0x7f855db200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390803][0297][0x7f84b4d200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=1^[[0m
Jan 25 00:00:05 ar_logcat: aec index 1973751614 line 7042 gain 1.000000 
Jan 25 00:00:05 ar_logcat:  imx681_stream_on
Jan 25 00:00:05 ar_logcat:  imx681_trigger_on
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390803][0297][0x7f84b4d200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390803][0297][0x7f84a66200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:05 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:05 ar_logcat: ^[[31;22m[25 00:00:05.617480][0488][0x7f7affd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:05 ar_logcat: ^[[31;22m[25 00:00:05.617754][0488][0x7f7affd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:05 ar_logcat: ^[[31;22m[25 00:00:05.618755][0488][0x7f7affd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:05 ar_logcat: ^[[31;22m[25 00:00:05.618852][0488][0x7f7affd1d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390803][0297][0x7f84aa8200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390803][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390804][0297][0x7f855ba200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390804][0297][0x7f85578200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390804][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390804][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:05 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390804][0297][0x7f85578200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:05 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:05 ar_logcat: 35f41000 3615f000 361e8000 0 0 0 3626b000 36489000 36512000 0 0 0 
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390804][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:05 ar_logcat: aec index 1973751614 line 1125 gain 1.000000 
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390804][0297][0x7f85578200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390804][0297][0x7f85578200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:05 ar_logcat: 3659b000 367b9000 36842000 0 0 0 368c5000 36ae3000 36b6c000 0 0 0 
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390806][0297][0x7f85927200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390807][0297][0x7f85927200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390809][0297][0x7f85927200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390809][0297][0x7f85927200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 2^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390815][0297][0x7f85927200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390815][0297][0x7f85927200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390819][0297][0x7f85927200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390819][0297][0x7f85927200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390820][0297][0x7f84c95200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390821][0297][0x7f84c95200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390823][0519][0x7f81fc6010][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:05 ar_logcat: ^[[33;22m[124390823][0297][0x7f84b0b200][VO_CORE][WARN] display_api.c: dc_init : 2258 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:05 ar_logcat: ^[[33;22m[124390824][0297][0x7f84b0b200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390824][0297][0x7f84b0b200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:[0297][0x7f84a87200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390824][0297][0x7f84b4d200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390824][0297][0x7f84b4d200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390825][0297][0x7f84b4d200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390825][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390825][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:05 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390825][0297][0x7f84b4d200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:05 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:05 ar_logcat: 35f41000 3615f000 361e8000 0 0 0 3626b000 36489000 36512000 0 0 0 
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390825][0297][0x7f85557200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:05 ar_logcat: aec index 778170271 line 1125 gain 1.000000 
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390825][0297][0x7f84b4d200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390825][0297][0x7f84b4d200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:05 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390826][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:05 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:05 ar_logcat: 3659b000 367b9000 36842000 0 0 0 368c5000 36ae3000 36b6c000 0 0 0 
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390827][0297][0x7f85927200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:05 ar_logcat: ^[[33;22m[124390828][0297][0x7f84cb6200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_enable : 456 subvision already enable!^[[0m
Jan 25 00:00:05 ar_logcat: ^[[35;22m[124390828][0297][0x7f85927200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:05 ar_logcat: ^[[33;22m[124390830][0297][0x7f84cb6200][VO_CORE][WARN] display_top.c: display_offset_vo_src : 1520 hardware_diff_us:6635.160156 first_skewing_us:6635.160156!
Jan 25 00:00:05 ar_logcat: ^[[0m
Jan 25 00:00:05 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390885][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:05 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:05 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:00:05 ar_logcat: ^[[31;22m[124390888][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:05 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:06 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:06 ar_logcat: ^[[31;22m[124390940][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:06 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:28 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:00:28 ar_logcat: ^[[31;22m[124393104][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:28 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:28 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:28 ar_logcat: ^[[31;22m[124393144][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:28 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:31 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:00:31 ar_logcat: ^[[31;22m[124393445][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:31 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:32 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:32 ar_logcat: ^[[31;22m[124393538][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:32 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:39 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:00:39 ar_logcat: ^[[31;22m[124394287][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:39 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:49 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:49 ar_logcat: ^[[31;22m[124395234][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:49 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:50 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:00:50 ar_logcat: ^[[31;22m[124395320][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:50 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:01:12 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:01:12 ar_logcat: ^[[31;22m[124397552][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:12 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:01:15 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:01:15 ar_logcat: ^[[31;22m[124397797][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:15 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:01:16 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:01:16 ar_logcat: ^[[31;22m[124397898][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:16 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:01:19 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:01:19 ar_logcat: ^[[31;22m[124398231][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:19 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:01:28 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:01:28 ar_logcat: ^[[31;22m[124399171][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:28 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:01:31 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:01:31 ar_logcat: ^[[31;22m[124399459][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:31 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:01:33 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:01:33 ar_logcat: ^[[31;22m[124399632][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:33 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:01:39 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:01:39 ar_logcat: ^[[31;22m[124400244][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:39 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:01:43 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:01:43 ar_logcat: ^[[31;22m[124400693][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:43 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:01:46 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:01:46 ar_logcat: ^[[31;22m[124400954][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:46 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:01:48 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:01:48 ar_logcat: ^[[31;22m[124401112][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:48 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:01:53 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:01:53 ar_logcat: ^[[31;22m[124401621][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:53 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:01:53 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:01:53 ar_logcat: ^[[31;22m[124401669][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:53 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:01:54 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:01:54 ar_logcat: ^[[31;22m[124401784][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:54 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:01:55 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:01:55 ar_logcat: ^[[31;22m[124401857][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:01:55 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:09 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:02:09 ar_logcat: ^[[31;22m[124403279][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:09 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:02:16 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:02:16 ar_logcat: ^[[31;22m[124403948][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:16 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:16 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:02:16 ar_logcat: ^[[31;22m[124403962][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:16 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:02:17 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:02:17 ar_logcat: ^[[31;22m[124404037][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:17 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:20 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:02:20 ar_logcat: ^[[31;22m[124404299][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:20 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:02:20 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:02:20 ar_logcat: ^[[31;22m[124404304][0297][0x7f85927200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:20 ar_logcat: out and in is not equal, use uvsmooth lut table 
