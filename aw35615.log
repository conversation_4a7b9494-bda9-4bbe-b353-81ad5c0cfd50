[    0.381328] sw reset
[    0.382231] sw reset
[    0.383519] aw35615 device ID: 0x91
[    0.384650] pd := off
[    0.384652] vbus is already Off
[    0.384653] charge is already Off
[    0.384655] vconn is already Off
[    0.384870] pd header := Sink, Device
[    0.384879] cc1=Open, cc2=Open
[    0.386112] pd := off
[    0.386116] vbus is already Off
[    0.386117] charge is already Off
[    0.386118] vconn is already Off
[    0.386342] pd header := Sink, Device
[    0.386349] cc := Rd
[    0.388850] start drp toggling
[    0.389599] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.389601] IRQ: VBUS_OK, vbus=On
[    0.389609] gpio_intn_value:0
[    0.389718] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.390256] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.390258] IRQ: VBUS_OK, vbus=On
[    0.390260] gpio_intn_value:1
[    0.390368] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.396843] IRQ: 0x00, a: 0x40, b: 0x00, status0: 0x82, status1: 0x28
[    0.396847] IRQ: TOGDONE
[    0.398274] detected cc1=Open, cc2=Rp-1.5
[    0.398277] gpio_intn_value:0
[    0.398385] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.398925] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0x82, status1: 0x28
[    0.398928] gpio_intn_value:1
[    0.399037] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.399041] cc1=Open, cc2=Rp-1.5
[    0.479335] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.479340] IRQ: BC_LVL, handler pending
[    0.479347] gpio_intn_value:0
[    0.479457] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.480015] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.480017] IRQ: BC_LVL, handler pending
[    0.480020] gpio_intn_value:1
[    0.480131] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.480988] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.480991] IRQ: BC_LVL, handler pending
[    0.480993] gpio_intn_value:0
[    0.481105] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.481653] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.481656] IRQ: BC_LVL, handler pending
[    0.481657] gpio_intn_value:1
[    0.481772] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.482642] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.482644] IRQ: BC_LVL, handler pending
[    0.482646] gpio_intn_value:0
[    0.482757] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.483307] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.483310] IRQ: BC_LVL, handler pending
[    0.483312] gpio_intn_value:1
[    0.483421] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.518600] BC_LVL handler, status0=0x92
[    0.584884] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.584900] IRQ: BC_LVL, handler pending
[    0.584917] gpio_intn_value:0
[    0.585055] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.585623] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.585629] IRQ: BC_LVL, handler pending
[    0.585637] gpio_intn_value:1
[    0.585746] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.586469] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.586489] IRQ: BC_LVL, handler pending
[    0.586500] gpio_intn_value:0
[    0.586622] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.587181] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.587187] IRQ: BC_LVL, handler pending
[    0.587195] gpio_intn_value:1
[    0.587305] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.588258] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.588266] IRQ: BC_LVL, handler pending
[    0.588275] gpio_intn_value:0
[    0.588386] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.588952] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.588956] IRQ: BC_LVL, handler pending
[    0.588962] gpio_intn_value:1
[    0.589071] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.599385] pd header := Sink, Device
[    0.599429] vbus is already Off
[    0.600603] pd := on
[    0.638582] BC_LVL handler, status0=0x92
[    0.690198] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc0, status1: 0x08
[    0.690209] IRQ: BC_LVL, handler pending
[    0.690215] gpio_intn_value:0
[    0.690324] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.690863] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.690866] IRQ: BC_LVL, handler pending
[    0.690867] gpio_intn_value:0
[    0.690976] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.691514] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.691516] IRQ: BC_LVL, handler pending
[    0.691517] IRQ: PD sent good CRC
[    0.692114] PD message header: 17a1 len:4 crc:2db8a2a
[    0.692119] gpio_intn_value:1
[    0.692277] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.692936] sending PD message header: 1042
[    0.692940] sending PD message len: 4
[    0.693682] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x28
[    0.693685] IRQ: BC_LVL, handler pending
[    0.693691] gpio_intn_value:0
[    0.693800] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.694342] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.694345] IRQ: BC_LVL, handler pending
[    0.694347] gpio_intn_value:0
[    0.694458] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.694997] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xd1, status1: 0x08
[    0.694999] IRQ: BC_LVL, handler pending
[    0.695000] IRQ: PD tx success
[    0.695418] PD message header: 1a1 len:0 crc:81c2afc1
[    0.695425] gpio_intn_value:0
[    0.695534] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.696073] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.696075] IRQ: BC_LVL, handler pending
[    0.696076] gpio_intn_value:0
[    0.696185] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.696723] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0xd2, status1: 0x08
[    0.696725] IRQ: BC_LVL, handler pending
[    0.696726] IRQ: PD sent good CRC
[    0.697143] PD message header: 963 len:0 crc:76d5923f
[    0.697146] gpio_intn_value:0
[    0.697255] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.697794] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.697796] IRQ: BC_LVL, handler pending
[    0.697797] IRQ: PD sent good CRC
[    0.698214] PD message header: b66 len:0 crc:e5ac0756
[    0.698217] gpio_intn_value:0
[    0.698351] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.698912] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x28
[    0.698919] IRQ: BC_LVL, handler pending
[    0.698927] gpio_intn_value:1
[    0.699038] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.738597] BC_LVL handler, status0=0x92
[    0.748714] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.748722] IRQ: BC_LVL, handler pending
[    0.748730] gpio_intn_value:0
[    0.748843] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.749395] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.749399] IRQ: BC_LVL, handler pending
[    0.749405] gpio_intn_value:0
[    0.749515] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.750055] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.750057] IRQ: BC_LVL, handler pending
[    0.750058] IRQ: PD sent good CRC
[    0.750669] PD message header: 1d6f len:4 crc:a49bdc77
[    0.750677] gpio_intn_value:1
[    0.750791] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.751783] sending PD message header: 524f
[    0.751786] sending PD message len: 20
[    0.752671] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.752674] IRQ: BC_LVL, handler pending
[    0.752678] gpio_intn_value:0
[    0.752787] aw35615_i2c_read AW_REG_STATUS1 :0x20
[    0.753354] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.753357] IRQ: BC_LVL, handler pending
[    0.753359] gpio_intn_value:0
[    0.753468] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.754009] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.754012] IRQ: BC_LVL, handler pending
[    0.754015] IRQ: PD tx success
[    0.754436] PD message header: 361 len:0 crc:a43619a3
[    0.754445] gpio_intn_value:0
[    0.754555] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.755096] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.755100] IRQ: BC_LVL, handler pending
[    0.755105] gpio_intn_value:0
[    0.755217] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.755764] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.755768] IRQ: BC_LVL, handler pending
[    0.755771] IRQ: PD sent good CRC
[    0.756374] PD message header: 1f6f len:4 crc:ccee20f9
[    0.756383] gpio_intn_value:0
[    0.756503] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.757058] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x28
[    0.757062] IRQ: BC_LVL, handler pending
[    0.757071] gpio_intn_value:1
[    0.757184] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.757900] sending PD message header: 244f
[    0.757904] sending PD message len: 8
[    0.758719] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.758724] IRQ: BC_LVL, handler pending
[    0.758731] gpio_intn_value:0
[    0.758843] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.759390] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.759395] IRQ: BC_LVL, handler pending
[    0.759397] gpio_intn_value:0
[    0.759506] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.760044] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xd1, status1: 0x08
[    0.760046] IRQ: BC_LVL, handler pending
[    0.760047] IRQ: PD tx success
[    0.760465] PD message header: 561 len:0 crc:4d55bc96
[    0.760473] gpio_intn_value:0
[    0.760582] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.761120] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.761122] IRQ: BC_LVL, handler pending
[    0.761123] gpio_intn_value:0
[    0.761232] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.761769] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.761771] IRQ: BC_LVL, handler pending
[    0.761772] IRQ: PD sent good CRC
[    0.762366] PD message header: 116f len:4 crc:d279c8bc
[    0.762370] gpio_intn_value:1
[    0.762479] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.763174] sending PD message header: 264f
[    0.763176] sending PD message len: 8
[    0.763770] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.763772] IRQ: BC_LVL, handler pending
[    0.763773] gpio_intn_value:0
[    0.763882] aw35615_i2c_read AW_REG_STATUS1 :0x20
[    0.764419] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.764422] IRQ: BC_LVL, handler pending
[    0.764423] gpio_intn_value:0
[    0.764531] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.765069] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.765071] IRQ: BC_LVL, handler pending
[    0.765072] IRQ: PD tx success
[    0.765489] PD message header: 761 len:0 crc:a35bddba
[    0.765494] gpio_intn_value:0
[    0.765603] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.766141] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.766143] IRQ: BC_LVL, handler pending
[    0.766144] gpio_intn_value:1
[    0.766252] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.766254] IRQ: RX not empty and intn high!!!!!!!!!!!!
[    0.766848] PD message header: 136f len:4 crc:34acc952
[    0.767003] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.767637] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x28
[    0.767641] IRQ: BC_LVL, handler pending
[    0.767645] IRQ: PD sent good CRC
[    0.768089] PD message header: 0 len:0 crc:0
[    0.768094] gpio_intn_value:1
[    0.768203] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.768811] sending PD message header: 184f
[    0.768813] sending PD message len: 4
[    0.769608] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.769611] IRQ: BC_LVL, handler pending
[    0.769615] gpio_intn_value:0
[    0.769723] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.770261] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.770263] IRQ: BC_LVL, handler pending
[    0.770264] gpio_intn_value:0
[    0.770373] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.770911] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xd2, status1: 0x08
[    0.770913] IRQ: BC_LVL, handler pending
[    0.770914] IRQ: PD tx success
[    0.771331] PD message header: 961 len:0 crc:44e3f0bd
[    0.771337] gpio_intn_value:0
[    0.771445] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.771987] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.771989] IRQ: BC_LVL, handler pending
[    0.771990] gpio_intn_value:0
[    0.772099] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.772636] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.772638] IRQ: BC_LVL, handler pending
[    0.772639] IRQ: PD sent good CRC
[    0.773327] PD message header: 256f len:8 crc:e05e5346
[    0.773330] gpio_intn_value:1
[    0.773439] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.774135] sending PD message header: 2a4f
[    0.774136] sending PD message len: 8
[    0.774868] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.774870] IRQ: BC_LVL, handler pending
[    0.774871] gpio_intn_value:0
[    0.774980] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.775517] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.775519] IRQ: BC_LVL, handler pending
[    0.775521] gpio_intn_value:0
[    0.775629] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.776167] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.776169] IRQ: BC_LVL, handler pending
[    0.776170] IRQ: PD tx success
[    0.776586] PD message header: b61 len:0 crc:aaed9191
[    0.776590] gpio_intn_value:0
[    0.776699] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.777236] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.777238] IRQ: BC_LVL, handler pending
[    0.777240] gpio_intn_value:0
[    0.777348] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.777886] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.777888] IRQ: BC_LVL, handler pending
[    0.777889] IRQ: PD sent good CRC
[    0.778579] PD message header: 276f len:8 crc:98dceb3b
[    0.778582] gpio_intn_value:0
[    0.778692] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.779232] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0x92, status1: 0x28
[    0.779234] IRQ: BC_LVL, handler pending
[    0.779238] gpio_intn_value:1
[    0.779347] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.779953] sending PD message header: 1c4f
[    0.779955] sending PD message len: 4
[    0.780552] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.780554] IRQ: BC_LVL, handler pending
[    0.780556] gpio_intn_value:0
[    0.780664] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.781202] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.781204] IRQ: BC_LVL, handler pending
[    0.781205] gpio_intn_value:0
[    0.781314] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.781852] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.781854] IRQ: BC_LVL, handler pending
[    0.781855] IRQ: PD tx success
[    0.782273] PD message header: d61 len:0 crc:438e34a4
[    0.782278] gpio_intn_value:1
[    0.782386] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.809431] sending PD message header: 2e4f
[    0.809439] sending PD message len: 8
[    0.810146] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.810163] IRQ: BC_LVL, handler pending
[    0.810176] gpio_intn_value:0
[    0.810295] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.810875] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.810889] IRQ: BC_LVL, handler pending
[    0.810899] gpio_intn_value:0
[    0.811015] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.811613] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.811628] IRQ: BC_LVL, handler pending
[    0.811633] IRQ: PD tx success
[    0.812176] PD message header: f61 len:0 crc:ad805588
[    0.812204] gpio_intn_value:1
[    0.812339] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.848627] BC_LVL handler, status0=0x92
