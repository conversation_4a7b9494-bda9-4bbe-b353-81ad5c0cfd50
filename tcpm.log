[    0.383523] Setting usb_comm capable false
[    0.384657] Setting voltage/current limit 0 mV 0 mA
[    0.384659] polarity 0
[    0.384661] Requesting mux state 0, usb-role 0, orientation 0
[    0.384874] state change INVALID_STATE -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.384881] CC1: 0 -> 0, CC2: 0 -> 0 [state SNK_UNATTACHED, polarity 0, disconnected]
[    0.384885] 2-0022: registered
[    0.384887] Setting usb_comm capable false
[    0.386120] Setting voltage/current limit 0 mV 0 mA
[    0.386128] polarity 0
[    0.386130] Requesting mux state 0, usb-role 0, orientation 0
[    0.386347] cc:=2
[    0.387676] pending state change PORT_RESET -> PORT_RESET_WAIT_OFF @ 100 ms [rev1 NONE_AMS]
[    0.387683] state change PORT_RESET -> PORT_RESET_WAIT_OFF [delayed 100 ms]
[    0.387686] state change PORT_RESET_WAIT_OFF -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.387689] Start toggling
[    0.390372] VBUS on
[    0.399043] CC1: 0 -> 0, CC2: 0 -> 4 [state TOGGLING, polarity 0, connected]
[    0.399048] state change TOGGLING -> SNK_ATTACH_WAIT [rev1 NONE_AMS]
[    0.399052] pending state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED @ 200 ms [rev1 NONE_AMS]
[    0.599075] state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED [delayed 200 ms]
[    0.599088] state change SNK_DEBOUNCED -> SNK_ATTACHED [rev1 NONE_AMS]
[    0.599091] polarity 1
[    0.599094] Requesting mux state 1, usb-role 2, orientation 2
[    0.599392] state change SNK_ATTACHED -> SNK_STARTUP [rev1 NONE_AMS]
[    0.599415] state change SNK_STARTUP -> SNK_DISCOVERY [rev2 NONE_AMS]
[    0.599419] Setting voltage/current limit 5000 mV 1500 mA
[    0.599427] vbus=0 charge:=1
[    0.599431] state change SNK_DISCOVERY -> SNK_WAIT_CAPABILITIES [rev2 NONE_AMS]
[    0.600609] pending state change SNK_WAIT_CAPABILITIES -> HARD_RESET_SEND @ 620 ms [rev2 NONE_AMS]
[    0.692126] PD RX, header: 0x17a1 [1], 1 objects
[    0.692130] PD RX, object:0x36019096
[    0.692135]  PDO 0: type 0, 5000 mV, 1500 mA [RSHUD]
[    0.692281] state change SNK_WAIT_CAPABILITIES -> SNK_NEGOTIATE_CAPABILITIES [rev2 POWER_NEGOTIATION]
[    0.692289] Setting usb_comm capable true
[    0.692300] cc=2 cc1=0 cc2=4 vbus=0 vconn=sink polarity=1
[    0.692303] Requesting PDO 0: 5000 mV, 1500 mA [mismatch]
[    0.692305] PD TX, header: 0x1042
[    0.695421] PD TX complete, status: 0
[    0.695431] pending state change SNK_NEGOTIATE_CAPABILITIES -> HARD_RESET_SEND @ 60 ms [rev2 POWER_NEGOTIATION]
[    0.697148] PD RX, header: 0x963 [1], 0 objects
[    0.697154] state change SNK_NEGOTIATE_CAPABILITIES -> SNK_TRANSITION_SINK [rev2 POWER_NEGOTIATION]
[    0.697159] pending state change SNK_TRANSITION_SINK -> HARD_RESET_SEND @ 500 ms [rev2 POWER_NEGOTIATION]
[    0.698218] PD RX, header: 0xb66 [1], 0 objects
[    0.698223] Setting voltage/current limit 5000 mV 1500 mA
[    0.698233] state change SNK_TRANSITION_SINK -> SNK_READY [rev2 POWER_NEGOTIATION]
[    0.698373] AMS POWER_NEGOTIATION finished
[    0.750684] PD RX, header: 0x1d6f [1], 1 objects
[    0.750688] PD RX, object:0xff008001
[    0.750692] Rx VDM cmd 0xff008001 type 0 cmd 1 len 1 adev           (null)
[    0.750702] tcpm_queue_vdm
[    0.750705] vdm_run_state_machine vdm_state:1
[    0.750707] AMS DISCOVER_IDENTITY start
[    0.750710] vdm_run_state_machine vdm_state:4
[    0.750712] PD TX, header: 0x524f
[    0.754440] PD TX complete, status: 0
[    0.754448] AMS DISCOVER_IDENTITY finished
[    0.754458] vdm_run_state_machine vdm_state:2
[    0.754460] vdm_run_state_machine vdm_state:-1
[    0.756386] PD RX, header: 0x1f6f [1], 1 objects
[    0.756391] PD RX, object:0xff008002
[    0.756396] Rx VDM cmd 0xff008002 type 0 cmd 2 len 1 adev           (null)
[    0.756407] svid 0xff01
[    0.756409] tcpm_queue_vdm
[    0.756412] vdm_run_state_machine vdm_state:1
[    0.756415] AMS DISCOVER_SVIDS start
[    0.756418] vdm_run_state_machine vdm_state:4
[    0.756420] PD TX, header: 0x244f
[    0.760468] PD TX complete, status: 0
[    0.760481] AMS DISCOVER_SVIDS finished
[    0.760486] vdm_run_state_machine vdm_state:2
[    0.760487] vdm_run_state_machine vdm_state:-1
[    0.762372] PD RX, header: 0x116f [1], 1 objects
[    0.762376] PD RX, object:0xff018003
[    0.762380] Rx VDM cmd 0xff018003 type 0 cmd 3 len 1 adev           (null)
[    0.762388] SRC SVID 1: 0xff01
[    0.762390] tcpm_queue_vdm
[    0.762391] vdm_run_state_machine vdm_state:1
[    0.762393] AMS DISCOVER_MODES start
[    0.762395] vdm_run_state_machine vdm_state:4
[    0.762398] PD TX, header: 0x264f
[    0.765492] PD TX complete, status: 0
[    0.765496] AMS DISCOVER_MODES finished
[    0.765500] vdm_run_state_machine vdm_state:2
[    0.765501] vdm_run_state_machine vdm_state:-1
[    0.766855] PD RX, header: 0x136f [1], 1 objects
[    0.766857] PD RX, object:0xff018104
[    0.766858] Rx VDM cmd 0xff018104 type 0 cmd 4 len 1 adev ffffffc030ba4008
[    0.766861]  Alternate mode 0: SVID 0xff01, VDO 1: 0x00000405
[    0.767110] tcpm_queue_vdm
[    0.767114] vdm_run_state_machine vdm_state:1
[    0.767115] AMS DFP_TO_UFP_ENTER_MODE start
[    0.767117] vdm_run_state_machine vdm_state:4
[    0.767118] PD TX, header: 0x184f
[    0.771334] PD TX complete, status: 0
[    0.771339] AMS DFP_TO_UFP_ENTER_MODE finished
[    0.771345] vdm_run_state_machine vdm_state:2
[    0.771346] vdm_run_state_machine vdm_state:-1
[    0.773332] PD RX, header: 0x256f [1], 2 objects
[    0.773337] PD RX, object:0xff018110
[    0.773338] PD RX, object:0x1
[    0.773341] Rx VDM cmd 0xff018110 type 0 cmd 16 len 2 adev ffffffc030ba4008
[    0.773372] tcpm_queue_vdm
[    0.773377] vdm_run_state_machine vdm_state:1
[    0.773379] AMS STRUCTURED_VDMS start
[    0.773381] vdm_run_state_machine vdm_state:4
[    0.773383] PD TX, header: 0x2a4f
[    0.776588] PD TX complete, status: 0
[    0.776592] AMS STRUCTURED_VDMS finished
[    0.776596] vdm_run_state_machine vdm_state:2
[    0.776597] vdm_run_state_machine vdm_state:-1
[    0.778584] PD RX, header: 0x276f [1], 2 objects
[    0.778588] PD RX, object:0xff018111
[    0.778589] PD RX, object:0x406
[    0.778590] Rx VDM cmd 0xff018111 type 0 cmd 17 len 2 adev ffffffc030ba4008
[    0.778617] tcpm_queue_vdm
[    0.778657] vdm_run_state_machine vdm_state:1
[    0.778659] AMS STRUCTURED_VDMS start
[    0.778660] vdm_run_state_machine vdm_state:4
[    0.778661] PD TX, header: 0x1c4f
[    0.782275] PD TX complete, status: 0
[    0.782280] AMS STRUCTURED_VDMS finished
[    0.782286] vdm_run_state_machine vdm_state:2
[    0.782287] vdm_run_state_machine vdm_state:-1
[    0.808565] tcpm_queue_vdm
[    0.808644] vdm_run_state_machine vdm_state:1
[    0.808647] AMS ATTENTION start
[    0.808651] vdm_run_state_machine vdm_state:4
[    0.808653] PD TX, header: 0x2e4f
[    0.812190] PD TX complete, status: 0
[    0.812209] AMS ATTENTION finished
[    0.842272] vdm_run_state_machine vdm_state:2
[    0.842277] vdm_run_state_machine vdm_state:-1
