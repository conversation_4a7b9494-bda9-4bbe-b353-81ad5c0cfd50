Jan 25 00:00:00 kernel: klogd started: BusyBox v1.25.0 (2025-06-23 10:50:06 CST)
Jan 25 00:00:00 kernel: [    0.000000] Booting Linux on physical CPU 0x0
Jan 25 00:00:00 kernel: [    0.000000] Linux version 4.9.38 (xreal@a15bbc1ccbbe) (gcc version 7.5.0 (Linaro GCC 7.5-2019.12) ) #3 SMP Mon Jun 23 10:47:47 CST 2025
Jan 25 00:00:00 kernel: [    0.000000] Boot CPU: AArch64 Processor [410fd034]
Jan 25 00:00:00 kernel: [    0.000000] Memory limited to 304MB
Jan 25 00:00:00 kernel: [    0.000000] Icc memory setup at 0x0000000024000000 size 0x0000000000000800 KB
Jan 25 00:00:00 kernel: [    0.000000] OF: reserved mem: initialized node icc@0x24000000, compatible id icc-region
Jan 25 00:00:00 kernel: [    0.000000] cma: Reserved 16 MiB at 0x0000000032000000
Jan 25 00:00:00 kernel: [    0.000000] On node 0 totalpages: 77822
Jan 25 00:00:00 kernel: [    0.000000]   DMA zone: 1216 pages used for memmap
Jan 25 00:00:00 kernel: [    0.000000]   DMA zone: 0 pages reserved
Jan 25 00:00:00 kernel: [    0.000000]   DMA zone: 77822 pages, LIFO batch:15
Jan 25 00:00:00 kernel: [    0.000000] percpu: Embedded 23 pages/cpu @ffffffc031f60000 s53400 r8192 d32616 u94208
Jan 25 00:00:00 kernel: [    0.000000] pcpu-alloc: s53400 r8192 d32616 u94208 alloc=23*4096
Jan 25 00:00:00 kernel: [    0.000000] pcpu-alloc: [0] 0 [0] 1 [0] 2 [0] 3 
Jan 25 00:00:00 kernel: [    0.000000] Detected VIPT I-cache on CPU0
Jan 25 00:00:00 kernel: [    0.000000] CPU features: enabling workaround for ARM erratum 845719
Jan 25 00:00:00 kernel: [    0.000000] Built 1 zonelists in Zone order, mobility grouping on.  Total pages: 76606
Jan 25 00:00:00 kernel: [    0.000000] Kernel command line: console=ttyS0,disable,earlyprintk loglevel=1,quiet root=/dev/mmcblk0p19 rootwait rw rootfstype=ext4 gpt mem=304m flagfile=/usrdata/sirius-clean-system-flag nmi_watchdog=panic part_info=34952
Jan 25 00:00:00 kernel: [    0.000000] PID hash table entries: 2048 (order: 2, 16384 bytes)
Jan 25 00:00:00 kernel: [    0.000000] Dentry cache hash table entries: 65536 (order: 7, 524288 bytes)
Jan 25 00:00:00 kernel: [    0.000000] Inode-cache hash table entries: 32768 (order: 6, 262144 bytes)
Jan 25 00:00:00 kernel: [    0.000000] Memory: 225676K/311288K available (7550K kernel code, 450K rwdata, 1820K rodata, 2048K init, 359K bss, 69228K reserved, 16384K cma-reserved)
Jan 25 00:00:00 kernel: [    0.000000] Virtual kernel memory layout:
Jan 25 00:00:00 kernel: [    0.000000]     modules : 0xffffff8000000000 - 0xffffff8008000000   (   128 MB)
Jan 25 00:00:00 kernel: [    0.000000]     vmalloc : 0xffffff8008000000 - 0xffffffbebfff0000   (   250 GB)
Jan 25 00:00:00 kernel: [    0.000000]       .text : 0xffffff80080a0000 - 0xffffff8008800000   (  7552 KB)
Jan 25 00:00:00 kernel: [    0.000000]     .rodata : 0xffffff8008800000 - 0xffffff8008a00000   (  2048 KB)
Jan 25 00:00:00 kernel: [    0.000000]       .init : 0xffffff8008a00000 - 0xffffff8008c00000   (  2048 KB)
Jan 25 00:00:00 kernel: [    0.000000]       .data : 0xffffff8008c00000 - 0xffffff8008c70808   (   451 KB)
Jan 25 00:00:00 kernel: [    0.000000]        .bss : 0xffffff8008c70808 - 0xffffff8008cca79c   (   360 KB)
Jan 25 00:00:00 kernel: [    0.000000]     fixed   : 0xffffffbefe7fd000 - 0xffffffbefec00000   (  4108 KB)
Jan 25 00:00:00 kernel: [    0.000000]     PCI I/O : 0xffffffbefee00000 - 0xffffffbeffe00000   (    16 MB)
Jan 25 00:00:00 kernel: [    0.000000]     vmemmap : 0xffffffbf00000000 - 0xffffffc000000000   (     4 GB maximum)
Jan 25 00:00:00 kernel: [    0.000000]               0xffffffbf00800000 - 0xffffffbf00cc0000   (     4 MB actual)
Jan 25 00:00:00 kernel: [    0.000000]     memory  : 0xffffffc020000000 - 0xffffffc033000000   (   304 MB)
Jan 25 00:00:00 kernel: [    0.000000] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=4, Nodes=1
Jan 25 00:00:00 kernel: [    0.000000] Hierarchical RCU implementation.
Jan 25 00:00:00 kernel: [    0.000000] 	Build-time adjustment of leaf fanout to 64.
Jan 25 00:00:00 kernel: [    0.000000] NR_IRQS:64 nr_irqs:64 0
Jan 25 00:00:00 kernel: [    0.000000] arm_arch_timer: Architected cp15 timer(s) running at 24.00MHz (virt).
Jan 25 00:00:00 kernel: [    0.000000] clocksource: arch_sys_counter: mask: 0xffffffffffffff max_cycles: 0x588fe9dc0, max_idle_ns: 440795202592 ns
Jan 25 00:00:00 kernel: [    0.000002] sched_clock: 56 bits at 24MHz, resolution 41ns, wraps every 4398046511097ns
Jan 25 00:00:00 kernel: [    0.000151] Console: colour dummy device 80x25
Jan 25 00:00:00 kernel: [    0.000166] Calibrating delay loop (skipped), value calculated using timer frequency.. 48.00 BogoMIPS (lpj=240000)
Jan 25 00:00:00 kernel: [    0.000170] pid_max: default: 32768 minimum: 301
Jan 25 00:00:00 kernel: [    0.000212] Mount-cache hash table entries: 1024 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.000214] Mountpoint-cache hash table entries: 1024 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.000662] ASID allocator initialised with 65536 entries
Jan 25 00:00:00 kernel: [    0.001590] Detected VIPT I-cache on CPU1
Jan 25 00:00:00 kernel: [    0.001626] CPU1: Booted secondary processor [410fd034]
Jan 25 00:00:00 kernel: [    0.001762] Detected VIPT I-cache on CPU2
Jan 25 00:00:00 kernel: [    0.001773] CPU2: Booted secondary processor [410fd034]
Jan 25 00:00:00 kernel: [    0.001896] Detected VIPT I-cache on CPU3
Jan 25 00:00:00 kernel: [    0.001907] CPU3: Booted secondary processor [410fd034]
Jan 25 00:00:00 kernel: [    0.001928] Brought up 4 CPUs
Jan 25 00:00:00 kernel: [    0.001930] SMP: Total of 4 processors activated.
Jan 25 00:00:00 kernel: [    0.001933] CPU features: detected feature: 32-bit EL0 Support
Jan 25 00:00:00 kernel: [    0.001936] CPU: All CPU(s) started at EL1
Jan 25 00:00:00 kernel: [    0.001945] alternatives: patching kernel code
Jan 25 00:00:00 kernel: [    0.002265] devtmpfs: initialized
Jan 25 00:00:00 kernel: [    0.006247] clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 19112604462750000 ns
Jan 25 00:00:00 kernel: [    0.006260] futex hash table entries: 1024 (order: 5, 131072 bytes)
Jan 25 00:00:00 kernel: [    0.007287] NET: Registered protocol family 16
Jan 25 00:00:00 kernel: [    0.007824] vdso: 2 pages (1 code @ ffffff8008807000, 1 data @ ffffff8008c04000)
Jan 25 00:00:00 kernel: [    0.007832] hw-breakpoint: found 6 breakpoint and 4 watchpoint registers.
Jan 25 00:00:00 kernel: [    0.008166] DMA: preallocated 256 KiB pool for atomic allocations
Jan 25 00:00:00 kernel: [    0.015466] dw_dmac 1e10000.ahb_dma: DesignWare DMA Controller, 8 channels
Jan 25 00:00:00 kernel: [    0.015996] SCSI subsystem initialized
Jan 25 00:00:00 kernel: [    0.016069] usbcore: registered new interface driver usbfs
Jan 25 00:00:00 kernel: [    0.016086] usbcore: registered new interface driver hub
Jan 25 00:00:00 kernel: [    0.016111] usbcore: registered new device driver usb
Jan 25 00:00:00 kernel: [    0.017589] Linux video capture interface: v2.00
Jan 25 00:00:00 kernel: [    0.017761] icc-artosyn icc-artosyn: assigned reserved memory node icc@0x24000000
Jan 25 00:00:00 kernel: [    0.018099] Advanced Linux Sound Architecture Driver Initialized.
Jan 25 00:00:00 kernel: [    0.018451] clocksource: Switched to clocksource arch_sys_counter
Jan 25 00:00:00 kernel: [    0.019575] NET: Registered protocol family 2
Jan 25 00:00:00 kernel: [    0.019798] TCP established hash table entries: 4096 (order: 3, 32768 bytes)
Jan 25 00:00:00 kernel: [    0.019814] TCP bind hash table entries: 4096 (order: 4, 65536 bytes)
Jan 25 00:00:00 kernel: [    0.019873] TCP: Hash tables configured (established 4096 bind 4096)
Jan 25 00:00:00 kernel: [    0.019908] UDP hash table entries: 256 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.019915] UDP-Lite hash table entries: 256 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.020018] NET: Registered protocol family 1
Jan 25 00:00:00 kernel: [    0.020188] RPC: Registered named UNIX socket transport module.
Jan 25 00:00:00 kernel: [    0.020189] RPC: Registered udp transport module.
Jan 25 00:00:00 kernel: [    0.020190] RPC: Registered tcp transport module.
Jan 25 00:00:00 kernel: [    0.020191] RPC: Registered tcp NFSv4.1 backchannel transport module.
Jan 25 00:00:00 kernel: [    0.020558] hw perfevents: enabled with armv8_cortex_a53 PMU driver, 7 counters available
Jan 25 00:00:00 kernel: [    0.021100] workingset: timestamp_bits=62 max_order=16 bucket_order=0
Jan 25 00:00:00 kernel: [    0.024259] exFAT: file-system version 2.2.0-3arter97
Jan 25 00:00:00 kernel: [    0.024562] NFS: Registering the id_resolver key type
Jan 25 00:00:00 kernel: [    0.024579] Key type id_resolver registered
Jan 25 00:00:00 kernel: [    0.024580] Key type id_legacy registered
Jan 25 00:00:00 kernel: [    0.024586] nfs4filelayout_init: NFSv4 File Layout Driver Registering...
Jan 25 00:00:00 kernel: [    0.024602] fuse init (API version 7.26)
Jan 25 00:00:00 kernel: [    0.025820] io scheduler noop registered
Jan 25 00:00:00 kernel: [    0.025821] io scheduler deadline registered
Jan 25 00:00:00 kernel: [    0.025872] io scheduler cfq registered (default)
Jan 25 00:00:00 kernel: [    0.026248] artosyn_typec_comphy_probe 1275 0 ffffffc0313bf800
Jan 25 00:00:00 kernel: [    0.026412] artosyn_kuiper_usb2phy_probe 288 0 ffffffc0313bfc00
Jan 25 00:00:00 kernel: [    0.026478] artosyn_kuiper_usb2phy_probe 288 0 ffffffc0308bc000
Jan 25 00:00:00 kernel: [    0.027285] gpio-artosyn a10a000.gpio: get 16 irqs for all ports
Jan 25 00:00:00 kernel: [    0.031313] Serial: 8250/16550 driver, 8 ports, IRQ sharing disabled
Jan 25 00:00:00 kernel: [    0.032079] console [ttyS0] disabled
Jan 25 00:00:00 kernel: [    0.032098] 1500000.serial: ttyS0 at MMIO 0x1500000 (irq = 31, base_baud = 8333333) is a 16550A
Jan 25 00:00:00 kernel: [    0.032124] console [ttyS0] enabled
Jan 25 00:00:00 kernel: [    0.032289] 1504000.serial: ttyS2 at MMIO 0x1504000 (irq = 32, base_baud = 8333333) is a 16550A
Jan 25 00:00:00 kernel: [    0.033946] brd: module loaded
Jan 25 00:00:00 kernel: [    0.034442] loop: module loaded
Jan 25 00:00:00 kernel: [    0.034594] artosyn_dprx_probe 3731 -517
Jan 25 00:00:00 kernel: [    0.035270] fast boot device registered
Jan 25 00:00:00 kernel: [    0.035305] hdcp check device registered
Jan 25 00:00:00 kernel: [    0.035349] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY0 at 0xa098000 with size 0x400
Jan 25 00:00:00 kernel: [    0.035353] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY1 at 0xa098400 with size 0x400
Jan 25 00:00:00 kernel: [    0.035356] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY2 at 0xa09c000 with size 0x400
Jan 25 00:00:00 kernel: [    0.035359] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY3 at 0xa09c400 with size 0x400
Jan 25 00:00:00 kernel: [    0.035391] dphy_reg_misc_device registered
Jan 25 00:00:00 kernel: [    0.035460] hw_info:hw_id_chan not get! error:-517
Jan 25 00:00:00 kernel: [    0.035473] ia8201_init called
Jan 25 00:00:00 kernel: [    0.035500] ia8201_probe called
Jan 25 00:00:00 kernel: [    0.035524] ret : 0
Jan 25 00:00:00 kernel: [    0.035529] cdev init
Jan 25 00:00:00 kernel: [    0.035530] cdev add
Jan 25 00:00:00 kernel: [    0.035530] create class
Jan 25 00:00:00 kernel: [    0.035540] device create
Jan 25 00:00:00 kernel: [    0.035839] enter dw_spi_add_host
Jan 25 00:00:00 kernel: [    0.035980] spi_master spi0: will run message pump with realtime priority
Jan 25 00:00:00 kernel: [    0.036125] enter dw_spi_add_host
Jan 25 00:00:00 kernel: [    0.036231] spi_master spi1: will run message pump with realtime priority
Jan 25 00:00:00 kernel: [    0.036336] enter dw_spi_add_host
Jan 25 00:00:00 kernel: [    0.036439] spi_master spi2: will run message pump with realtime priority
Jan 25 00:00:00 kernel: [    0.036750] sony_oled_driver_init^M
Jan 25 00:00:00 kernel: [    0.036763] sony_oled_probe-0
Jan 25 00:00:00 kernel: [    0.036764] compatible: sony,ecx343
Jan 25 00:00:00 kernel: [    0.036786] Failed to get oled vin regulator
Jan 25 00:00:00 kernel: [    0.036787] init failed
Jan 25 00:00:00 kernel: [    0.036798] sony_oled_probe-0
Jan 25 00:00:00 kernel: [    0.036799] compatible: sony,ecx343
Jan 25 00:00:00 kernel: [    0.036805] Failed to get oled vin regulator
Jan 25 00:00:00 kernel: [    0.036806] init failed
Jan 25 00:00:00 kernel: [    0.036939] libphy: Fixed MDIO Bus: probed
Jan 25 00:00:00 kernel: [    0.037151] usbcore: registered new interface driver r8152
Jan 25 00:00:00 kernel: [    0.037240] artosyn-dwc3 18.artosyn_dwc3: force disable usb2_using_dwc3
Jan 25 00:00:00 kernel: [    0.037588] 8080000.usb supply vusb_d not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.037614] 8080000.usb supply vusb_a not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.037654] artosyn_kuiper_usb2phy_init 105
Jan 25 00:00:00 kernel: [    0.038225] dwc2 8080000.usb: EPs: 16, dedicated fifos, 3968 entries in SPRAM
Jan 25 00:00:00 kernel: [    0.038507] usbcore: registered new interface driver usb-storage
Jan 25 00:00:00 kernel: [    0.038600] hw_info: hw_id_get not cpmplete!!!
Jan 25 00:00:00 kernel: [    0.038601] artosyn_typec_probe hw_id get not complete, re-probe
Jan 25 00:00:00 kernel: [    0.038679] [aw35615] aw35615_probe 1854: aw35615_probe enter
Jan 25 00:00:00 kernel: [    0.038680] hw_info: hw_id_get not cpmplete!!!
Jan 25 00:00:00 kernel: [    0.038682] [aw35615] aw35615_probe 1858: aw35615_probe hw_id get not complete, re-probe
Jan 25 00:00:00 kernel: [    0.038744] i2c /dev entries driver
Jan 25 00:00:00 kernel: [    0.038997] sy_oled_driver_init^M
Jan 25 00:00:00 kernel: [    0.039017] ec_i2c_init 
Jan 25 00:00:00 kernel: [    0.039033] ec major : 0, minor : 0^M
Jan 25 00:00:00 kernel: [    0.039093] ec driver mode:1
Jan 25 00:00:00 kernel: [    0.040246] ec_probe^M
Jan 25 00:00:00 kernel: [    0.040289] usbcore: registered new interface driver uvcvideo
Jan 25 00:00:00 kernel: [    0.040289] USB Video Class driver (1.1.1)
Jan 25 00:00:00 kernel: [    0.040501] dw_wdt 1600000.wdt: using irq(33) mode!
Jan 25 00:00:00 kernel: [    0.041725] sdhci: Secure Digital Host Controller Interface driver
Jan 25 00:00:00 kernel: [    0.041728] sdhci: Copyright(c) Pierre Ossman
Jan 25 00:00:00 kernel: [    0.041729] Synopsys Designware Multimedia Card Interface Driver
Jan 25 00:00:00 kernel: [    0.041866] sdhci-pltfm: SDHCI platform and OF driver helper
Jan 25 00:00:00 kernel: [    0.042034] sdhci-kuiper 8050000.sdhci: Ignore voltage domain gpio.
Jan 25 00:00:00 kernel: [    0.042087] mmc0: Unknown controller version (5). You may experience problems.
Jan 25 00:00:00 kernel: [    0.042417] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 1600000
Jan 25 00:00:00 kernel: [    0.042420] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 1600000
Jan 25 00:00:00 kernel: [    0.042487] mmc0: SDHCI controller on 8050000.sdhci [8050000.sdhci] using ADMA
Jan 25 00:00:00 kernel: [    0.042738] hidraw: raw HID events driver (C) Jiri Kosina
Jan 25 00:00:00 kernel: [    0.042806] usbcore: registered new interface driver usbhid
Jan 25 00:00:00 kernel: [    0.042807] usbhid: USB HID core driver
Jan 25 00:00:00 kernel: [    0.042966] ashmem: initialized
Jan 25 00:00:00 kernel: [    0.043409] spi0.0 supply inven,vdd_ana not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.043430] spi0.0 supply inven,vcc_i2c not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.043459] inv_mpu: inv_mpu_probe: power on here.
Jan 25 00:00:00 kernel: [    0.043460] inv_mpu: inv_mpu_probe: power on.
Jan 25 00:00:00 kernel: [    0.087711] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 24000000
Jan 25 00:00:00 kernel: [    0.087752] clk_composite_determine_rate:144, fix_pll_600 best_parent_hw 600000000
Jan 25 00:00:00 kernel: [    0.087761] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 24000000
Jan 25 00:00:00 kernel: [    0.087762] clk_composite_determine_rate:144, fix_pll_600 best_parent_hw 600000000
Jan 25 00:00:00 kernel: [    0.087877] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 0
Jan 25 00:00:00 kernel: [    0.087929] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 1
Jan 25 00:00:00 kernel: [    0.087985] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 2
Jan 25 00:00:00 kernel: [    0.088040] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 3
Jan 25 00:00:00 kernel: [    0.088096] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 4
Jan 25 00:00:00 kernel: [    0.088151] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 5
Jan 25 00:00:00 kernel: [    0.090261] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 7
Jan 25 00:00:00 kernel: [    0.090286] sdhci-kuiper 8050000.sdhci: mmc0: Setting the tuning phase to 2
Jan 25 00:00:00 kernel: [    0.090334] mmc0: new HS200 MMC card at address 0001
Jan 25 00:00:00 kernel: [    0.090535] mmcblk0: mmc0:0001 58A421 3.65 GiB 
Jan 25 00:00:00 kernel: [    0.090601] mmcblk0boot0: mmc0:0001 58A421 partition 1 4.00 MiB
Jan 25 00:00:00 kernel: [    0.090658] mmcblk0boot1: mmc0:0001 58A421 partition 2 4.00 MiB
Jan 25 00:00:00 kernel: [    0.090720] mmcblk0rpmb: mmc0:0001 58A421 partition 3 16.0 MiB
Jan 25 00:00:00 kernel: [    0.092082] Alternate GPT is invalid, using primary GPT.
Jan 25 00:00:00 kernel: [    0.092111]  mmcblk0: p1 p2 p3 p4 p5 p6 p7 p8 p9 p10 p11 p12 p13 p14 p15 p16 p17 p18 p19 p20 p21 p22
Jan 25 00:00:00 kernel: [    0.168524] inv_mpu: whoami= 0
Jan 25 00:00:00 kernel: [    0.168552] inv_mpu: whoami= 0
Jan 25 00:00:00 kernel: [    0.168569] inv_mpu: whoami= 0
Jan 25 00:00:00 kernel: [    0.168585] inv-mpu-iio-spi spi0.0: inv_mpu_probe failed -19
Jan 25 00:00:00 kernel: [    0.168657] spi1.0 supply inven,vdd_ana not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.168684] spi1.0 supply inven,vcc_i2c not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.168726] inv_mpu: inv_mpu_probe: power on here.
Jan 25 00:00:00 kernel: [    0.168727] inv_mpu: inv_mpu_probe: power on.
Jan 25 00:00:00 kernel: [    0.288488] inv_mpu: whoami= e9
Jan 25 00:00:00 kernel: [    0.319042] random: fast init done
Jan 25 00:00:00 kernel: [    0.319757] inv_mpu: data_new=0 40, 4, 40,8
Jan 25 00:00:00 kernel: [    0.319870] inv_mpu: external clkin enable
Jan 25 00:00:00 kernel: [    0.319905] inv_mpu: INT2 pin mux set to CLKIN ret = 0
Jan 25 00:00:00 kernel: [    0.319941] inv_mpu: RTC MODE enable ret = 0
Jan 25 00:00:00 kernel: [    0.320141] inv_mpu: I3C STC MODE disable ret = 0
Jan 25 00:00:00 kernel: [    0.320344] inv_mpu: accel source set ret = 0
Jan 25 00:00:00 kernel: [    0.320548] inv_mpu: gyro source set ret = 0
Jan 25 00:00:00 kernel: [    0.320583] inv_mpu: AUX2 disable ret = 0
Jan 25 00:00:00 kernel: [    0.320584] inv_mpu: external clkin enable result is 0
Jan 25 00:00:00 kernel: [    0.328796] inv_mpu: write mag matrix data
Jan 25 00:00:00 kernel: [    0.334174] inv_mpu: inv_mpu_initialize: initialize result is 0....
Jan 25 00:00:00 kernel: [    0.334461] inv_mpu: wakeup_source is created successfully
Jan 25 00:00:00 kernel: [    0.334464] inv-mpu-iio-spi spi1.0: icm45600 ma-kernel-10.2.4 is ready to go!
Jan 25 00:00:00 kernel: [    0.334466] inv-mpu-iio-spi spi1.0: inv-mpu-iio clock type 1
Jan 25 00:00:00 kernel: [    0.334467] inv_mpu: Data read from FIFO
Jan 25 00:00:00 kernel: [    0.334536] mmc5603 5-0030: enter mmc5603_probe
Jan 25 00:00:00 kernel: [    0.334837] mmc5603 5-0030: MMC5603 chip id 10
Jan 25 00:00:00 kernel: [    0.351502] NET: Registered protocol family 10
Jan 25 00:00:00 kernel: [    0.351876] sit: IPv6, IPv4 and MPLS over IPv4 tunneling driver
Jan 25 00:00:00 kernel: [    0.352109] NET: Registered protocol family 17
Jan 25 00:00:00 kernel: [    0.352150] Key type dns_resolver registered
Jan 25 00:00:00 kernel: [    0.355692] artosyn_dprx_probe 3731 -517
Jan 25 00:00:00 kernel: [    0.355813] hw_info: hw_id_chan get!
Jan 25 00:00:00 kernel: [    0.360192] hw_info:read hw_id raw :1469
Jan 25 00:00:00 kernel: [    0.360299] Misc device registered: hw_info, minor number = 1013
Jan 25 00:00:00 kernel: [    0.360324] sony_oled_probe-0
Jan 25 00:00:00 kernel: [    0.360326] compatible: sony,ecx343
Jan 25 00:00:00 kernel: [    0.360370] of_get_named_gpio = 77
Jan 25 00:00:00 kernel: [    0.360373] gpio gpio-rst-77 request ok
Jan 25 00:00:00 kernel: [    0.360375] dev->rst_gpio77
Jan 25 00:00:00 kernel: [    0.370377] find label: ecx343_left
Jan 25 00:00:00 kernel: [    0.370485] check spi communication ok!
Jan 25 00:00:00 kernel: [    0.370489] major : 246, minor : 0
Jan 25 00:00:00 kernel: [    0.370492] cdev add successfully
Jan 25 00:00:00 kernel: [    0.370504] class create successfully
Jan 25 00:00:00 kernel: [    0.370550] device create successfully
Jan 25 00:00:00 kernel: [    0.370570] sony_oled_probe-1
Jan 25 00:00:00 kernel: [    0.370571] compatible: sony,ecx343
Jan 25 00:00:00 kernel: [    0.370593] of_get_named_gpio = 28
Jan 25 00:00:00 kernel: [    0.370594] gpio gpio-rst-28 request ok
Jan 25 00:00:00 kernel: [    0.370595] dev->rst_gpio28
Jan 25 00:00:00 kernel: [    0.380597] find label: ecx343_right
Jan 25 00:00:00 kernel: [    0.380659] check spi communication ok!
Jan 25 00:00:00 kernel: [    0.380660] major : 246, minor : 1
Jan 25 00:00:00 kernel: [    0.380661] cdev add successfully
Jan 25 00:00:00 kernel: [    0.380668] class create successfully
Jan 25 00:00:00 kernel: [    0.380701] device create successfully
Jan 25 00:00:00 kernel: [    0.380753] hw_info: hw_id : 6
Jan 25 00:00:00 kernel: [    0.380754] artosyn_typec_probe hw_id:6,pd disable
Jan 25 00:00:00 kernel: [    0.380831] artosyn_tcpc soc_internal_pd_disabled: 1
Jan 25 00:00:00 kernel: [    0.380898] artosyn_tcpc typec_base: ffffff8008f9a000, pd_base: ffffff8008fb2000
Jan 25 00:00:00 kernel: [    0.380900] artosyn_typec_probe 1860 success! ffffffc0312a8c10 ffffffc0313bf400 ffffffc030b7e1c0
Jan 25 00:00:00 kernel: [    0.380923] [aw35615] aw35615_probe 1854: aw35615_probe enter
Jan 25 00:00:00 kernel: [    0.380924] hw_info: hw_id : 6
Jan 25 00:00:00 kernel: [    0.380926] [aw35615] aw35615_probe 1866: aw35615_probe hw_id:6,aw ic exsit, continue
Jan 25 00:00:00 kernel: [    0.381059] tcpc_aw35615 2-0022: vid is correct, 0x91
Jan 25 00:00:00 kernel: [    0.381068] 2-0022 supply vbus not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.381137] xreal-PD Driver: aw35615_debugfs_init debugfs :aw35615-2-0022
Jan 25 00:00:00 kernel: [    0.381770] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.381930] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.381941] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.381992] connector altmodes
Jan 25 00:00:00 kernel: [    0.381993] altmodes altmodes
Jan 25 00:00:00 kernel: [    0.381997] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.382004] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.384664] artosyn_set_orientation 768 0
Jan 25 00:00:00 kernel: [    0.384674] artosyn_set_mux 799 mode 0 polarity 0
Jan 25 00:00:00 kernel: [    0.384676] artosyn-tcpc a124000.tcpc: dp altmode not register
Jan 25 00:00:00 kernel: [    0.384944] [aw35615] aw35615_probe 1977: probe ok
Jan 25 00:00:00 kernel: [    0.385030] artosyn_typec_reset_assert 1517 5
Jan 25 00:00:00 kernel: [    0.385277] dprx-artosyn a0a0000.dp: edid form ddr : magic mismatch
Jan 25 00:00:00 kernel: [    0.385283] dprx-artosyn a0a0000.dp: no valid edid in mmc, try to get edid from dts
Jan 25 00:00:00 kernel: [    0.385438] try load hdcp ret : 0, hdcp_key_length : 685
Jan 25 00:00:00 kernel: [    0.385441] dprx-artosyn a0a0000.dp: hdcp firmware load.
Jan 25 00:00:00 kernel: [    0.385669] artosyn_typec_event 1631 0!
Jan 25 00:00:00 kernel: [    0.385687] artosyn_dprx_probe 3949 success ffffffc0313bf400           (null)
Jan 25 00:00:00 kernel: [    0.385698] artosyn_dprx_dp_pd_event_work 2879 0
Jan 25 00:00:00 kernel: [    0.385996] input: gpio-keys as /devices/platform/gpio-keys/input/input0
Jan 25 00:00:00 kernel: [    0.386125] hctosys: unable to open rtc device (rtc0)
Jan 25 00:00:00 kernel: [    0.386135] artosyn_set_orientation 768 0
Jan 25 00:00:00 kernel: [    0.386147] artosyn_set_mux 799 mode 0 polarity 0
Jan 25 00:00:00 kernel: [    0.386149] artosyn-tcpc a124000.tcpc: dp altmode not register
Jan 25 00:00:00 kernel: [    0.386261] vmmc sdmmc1: disabling
Jan 25 00:00:00 kernel: [    0.386265] vqmmc sdmmc0: disabling
Jan 25 00:00:00 kernel: [    0.386267] vqmmc sdmmc1: disabling
Jan 25 00:00:00 kernel: [    0.386270] vqmmc sdhci: disabling
Jan 25 00:00:00 kernel: [    0.386272] eta355t: disabling
Jan 25 00:00:00 kernel: [    0.386274] eta355t: disabling
Jan 25 00:00:00 kernel: [    0.386276] eta355t: disabling
Jan 25 00:00:00 kernel: [    0.386279] ALSA device list:
Jan 25 00:00:00 kernel: [    0.386281]   No soundcards found.
Jan 25 00:00:00 kernel: [    0.449448] EXT4-fs (mmcblk0p19): recovery complete
Jan 25 00:00:00 kernel: [    0.449562] EXT4-fs (mmcblk0p19): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.449597] VFS: Mounted root (ext4 filesystem) on device 259:11.
Jan 25 00:00:00 kernel: [    0.450193] Freeing unused kernel memory: 2048K (ffffffc020a00000 - ffffffc020c00000)
Jan 25 00:00:00 kernel: [    0.468483] EXT4-fs (mmcblk0p19): re-mounted. Opts: data=ordered
Jan 25 00:00:00 kernel: [    0.545583] EXT4-fs (mmcblk0p5): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.599103] artosyn_set_orientation 768 2
Jan 25 00:00:00 kernel: [    0.599154] artosyn_set_mux 799 mode 1 polarity 1
Jan 25 00:00:00 kernel: [    0.599161] artosyn-tcpc a124000.tcpc: dp altmode not register
Jan 25 00:00:00 kernel: [    0.607092] EXT4-fs (mmcblk0p3): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.709106] EXT4-fs (mmcblk0p21): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.767059] typec_displayport_sink port0-partner.0: dp_sink_altmode_probe 552 ffffffc0313bf400
Jan 25 00:00:00 kernel: [    0.767080] dp_sink_altmode_vdm 318 cmdt 0 cmd 4 hdr ff018104
Jan 25 00:00:00 kernel: [    0.767081] dp_sink_altmode_vdm 320 vdo 0
Jan 25 00:00:00 kernel: [    0.767096] dp_sink_altmode_vdm 375 state 1
Jan 25 00:00:00 kernel: [    0.773353] dp_sink_altmode_vdm 318 cmdt 0 cmd 16 hdr ff018110
Jan 25 00:00:00 kernel: [    0.773358] dp_sink_altmode_vdm 320 vdo 1
Jan 25 00:00:00 kernel: [    0.773364] dp_sink_altmode_vdm 375 state 2
Jan 25 00:00:00 kernel: [    0.778594] dp_sink_altmode_vdm 318 cmdt 0 cmd 17 hdr ff018111
Jan 25 00:00:00 kernel: [    0.778596] dp_sink_altmode_vdm 320 vdo 406
Jan 25 00:00:00 kernel: [    0.778598] dp_sink_altmode_vdm 375 state 3
Jan 25 00:00:00 kernel: [    0.778605] artosyn_set_mux 799 mode 0 polarity 1
Jan 25 00:00:00 kernel: [    0.778616] dp_sink_altmode_configure_vdm 191 0x406
Jan 25 00:00:00 kernel: [    0.778621] dp_sink_altmode_notify 106 0x4 2 4
Jan 25 00:00:00 kernel: [    0.778622] artosyn_set_mux 799 mode 4 polarity 1
Jan 25 00:00:00 kernel: [    0.778628] artosyn_typec_event 1631 1!
Jan 25 00:00:00 kernel: [    0.778634] dp_sink_altmode_event 485 1 dp->hpd:0 hpd:0 irq:0
Jan 25 00:00:00 kernel: [    0.778667] artosyn_dprx_dp_pd_event_work 2879 1
Jan 25 00:00:00 kernel: [    0.778671] dp random: 7264119b5065c14c
Jan 25 00:00:00 kernel: [    0.778695] artosyn_dprx_init_phy 813 get lane info 4 4 1!
Jan 25 00:00:00 kernel: [    0.778701] artosyn_typec_comphy_init 627
Jan 25 00:00:00 kernel: [    0.778757] artosyn_typec_comphy_set_mode 713 dp mode
Jan 25 00:00:00 kernel: [    0.778825] artosyn_typec_reset_deassert 1546 5
Jan 25 00:00:00 kernel: [    0.796273] Adding 262140k swap on /usrdata/swapfile.  Priority:-1 extents:3 across:278524k SS
Jan 25 00:00:00 kernel: [    0.803942] ar_mpp_drv: loading out-of-tree module taints kernel.
Jan 25 00:00:00 kernel: [    0.808535] artosyn_dprx_init_ctrl 1225 success
Jan 25 00:00:00 kernel: [    0.808545] artosyn_typec_event 1631 1!
Jan 25 00:00:00 kernel: [    0.808554] dp_sink_altmode_event 485 1 dp->hpd:0 hpd:1 irq:0
Jan 25 00:00:00 kernel: [    0.808562] dp_sink_altmode_attention_vdm 300
Jan 25 00:00:00 kernel: [    0.808630] state change 0 -> 1
Jan 25 00:00:00 kernel: [    0.808635] artosyn_dprx_dp_pd_event_work 2879 1
Jan 25 00:00:00 kernel: [    0.831767] Module osal: init ok
Jan 25 00:00:00 kernel: [    0.831774] Media Memory Zone Manager
Jan 25 00:00:00 kernel: [    0.831948] osal 1.0 init success!
Jan 25 00:00:00 kernel: [    0.834678] ar_vb_init 0
Jan 25 00:00:00 kernel: [    0.836495] ar_sys_init 0
Jan 25 00:00:00 kernel: [    0.840750] camera_pwr_ioctl_init
Jan 25 00:00:00 kernel: [    0.840937] camera_pwr_ioctl_probe
Jan 25 00:00:00 kernel: [    0.840980] get camera_pwr success
Jan 25 00:00:00 kernel: [    0.841128] get camera_rst success
Jan 25 00:00:00 kernel: [    0.841262] camera_pwr_ioctl_probe complete.
Jan 25 00:00:00 kernel: [    0.843359]  imx681 timestamp_record_exp_init
Jan 25 00:00:00 kernel: [    0.843541]  ts_driver_probe
Jan 25 00:00:00 kernel: [    0.843584] get gpios_imx681_exp[27] success
Jan 25 00:00:00 kernel: [    0.843587] irq for gpio[27],irq[83]
Jan 25 00:00:00 kernel: [    0.845758]  imx681 camera_plug_detect_record_init
Jan 25 00:00:00 kernel: [    0.845944]  Camera_plug_detect driver probe
Jan 25 00:00:00 kernel: [    0.845987] get Camera plug detect gpio[13] success
Jan 25 00:00:00 kernel: [    0.845991] Camera plug detect irq for gpio[13],irq[69]
Jan 25 00:00:00 kernel: [    0.846767] camera plug_in_out_event_process_thread is running
Jan 25 00:00:00 kernel: [    0.848180] VO_PACK Init Start1...
Jan 25 00:00:00 kernel: [    0.848339] VO_PACK probe start.
Jan 25 00:00:00 kernel: [    0.848367] dev_0 start:8820000 size:100000 base: ffffff8009200000.
Jan 25 00:00:00 kernel: [    0.848409] dev0 irq:44 .
Jan 25 00:00:00 kernel: [    0.848421] dev_1 start:8840000 size:100000 base: ffffff8009400000.
Jan 25 00:00:00 kernel: [    0.848426] dev1 irq:45 .
Jan 25 00:00:00 kernel: [    0.848844] VO_PACK probe end.
Jan 25 00:00:00 kernel: [    0.911813] iqs323_init
Jan 25 00:00:00 kernel: [    0.911964] iqs323 3-0044: gpio gpio-rdy-57 request ok
Jan 25 00:00:00 kernel: [    0.956919] power up done 1
Jan 25 00:00:00 kernel: [    0.963579] dprx-artosyn a0a0000.dp: rd 0x0
Jan 25 00:00:00 kernel: [    0.963587] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 0
Jan 25 00:00:00 kernel: [    0.963795] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.963799] dprx-artosyn a0a0000.dp: wr 0 0 0 16
Jan 25 00:00:00 kernel: [    0.963933] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.964218] dprx-artosyn a0a0000.dp: rd 0x0
Jan 25 00:00:00 kernel: [    0.964221] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 0
Jan 25 00:00:00 kernel: [    0.964436] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.964439] dprx-artosyn a0a0000.dp: wr 0 0 0 16
Jan 25 00:00:00 kernel: [    0.966556] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.966883] dprx-artosyn a0a0000.dp: rd 0x10
Jan 25 00:00:00 kernel: [    0.966887] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 16
Jan 25 00:00:00 kernel: [    0.967122] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 16
Jan 25 00:00:00 kernel: [    0.967127] dprx-artosyn a0a0000.dp: wr 0 16 0 16
Jan 25 00:00:00 kernel: [    0.968562] iqs323 3-0044: gpio_num_57: using irq 113 for Cap Sensor rdy signal detection
Jan 25 00:00:00 kernel: [    0.968568] iqs323 3-0044: iqs323_reset is ready! 
Jan 25 00:00:00 kernel: [    0.968881] iqs323 3-0044: iqs323_probe is over : 0
Jan 25 00:00:00 kernel: [    0.969243] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.969533] dprx-artosyn a0a0000.dp: rd 0x20
Jan 25 00:00:00 kernel: [    0.969541] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 32
Jan 25 00:00:00 kernel: [    0.970054] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 32
Jan 25 00:00:00 kernel: [    0.970064] dprx-artosyn a0a0000.dp: wr 0 32 0 16
Jan 25 00:00:00 kernel: [    0.972189] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.972463] dprx-artosyn a0a0000.dp: rd 0x30
Jan 25 00:00:00 kernel: [    0.972469] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 48
Jan 25 00:00:00 kernel: [    0.972653] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 48
Jan 25 00:00:00 kernel: [    0.972657] dprx-artosyn a0a0000.dp: wr 0 48 0 16
Jan 25 00:00:00 kernel: [    0.974766] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.975035] dprx-artosyn a0a0000.dp: rd 0x40
Jan 25 00:00:00 kernel: [    0.975043] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 64
Jan 25 00:00:00 kernel: [    0.975230] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 64
Jan 25 00:00:00 kernel: [    0.975235] dprx-artosyn a0a0000.dp: wr 0 64 0 16
Jan 25 00:00:00 kernel: [    0.977358] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.977604] dprx-artosyn a0a0000.dp: rd 0x50
Jan 25 00:00:00 kernel: [    0.977610] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 80
Jan 25 00:00:00 kernel: [    0.977852] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 80
Jan 25 00:00:00 kernel: [    0.977859] dprx-artosyn a0a0000.dp: wr 0 80 0 16
Jan 25 00:00:00 kernel: [    0.979983] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.980255] dprx-artosyn a0a0000.dp: rd 0x60
Jan 25 00:00:00 kernel: [    0.980259] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 96
Jan 25 00:00:00 kernel: [    0.980462] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 96
Jan 25 00:00:00 kernel: [    0.980466] dprx-artosyn a0a0000.dp: wr 0 96 0 16
Jan 25 00:00:00 kernel: [    0.981829] ar_vb_open
Jan 25 00:00:00 kernel: [    0.981866] VB_EXITMCPL: not is_inited yet
Jan 25 00:00:00 kernel: [    0.981969] ar_vb_release
Jan 25 00:00:00 kernel: [    0.982006] ar_vb_open
Jan 25 00:00:00 kernel: [    0.982048] Create 0 common pools
Jan 25 00:00:00 kernel: [    0.982591] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.982853] dprx-artosyn a0a0000.dp: rd 0x70
Jan 25 00:00:00 kernel: [    0.982858] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 112
Jan 25 00:00:00 kernel: [    0.983047] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 112
Jan 25 00:00:00 kernel: [    0.983051] dprx-artosyn a0a0000.dp: wr 0 112 0 16
Jan 25 00:00:00 kernel: [    0.985159] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.991860] state change 1 -> 2
Jan 25 00:00:00 kernel: [    0.991873] pending state change 2 -> 3 @ 1000 ms
Jan 25 00:00:00 kernel: [    0.991879] link rate change 10000
Jan 25 00:00:00 kernel: [    0.991881] PHY 0x744: 068fc180
Jan 25 00:00:00 kernel: [    0.991882] 0x2208: 000000ff
Jan 25 00:00:00 kernel: [    0.991883] 0x2400: 00000000
Jan 25 00:00:00 kernel: [    0.991884] 0x2404: 00000000
Jan 25 00:00:00 kernel: [    0.991885] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.991886] 0x240c: 00000000
Jan 25 00:00:00 kernel: [    0.991887] 0x1104: 00000000
Jan 25 00:00:00 kernel: [    0.991889] 0x1108: 01000000
Jan 25 00:00:00 kernel: [    0.991890] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.991891] 0x1134: 01010000
Jan 25 00:00:00 kernel: [    0.991892] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.991893] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.991894] 0x1140: 8003f000
Jan 25 00:00:00 kernel: [    0.991895] PHY 0x3d8: 00000008
Jan 25 00:00:00 kernel: [    0.991896] PHY 0x43d8: 00000008
Jan 25 00:00:00 kernel: [    0.991897] PHY 0x83d8: 00000008
Jan 25 00:00:00 kernel: [    0.991898] PHY 0xc3d8: 00000008
Jan 25 00:00:00 kernel: [    0.991900] PHY 0x1414: 40400040
Jan 25 00:00:00 kernel: [    0.991901] PHY 0x44: 00000000
Jan 25 00:00:00 kernel: [    0.991902] PHY 0x20: 00006080
Jan 25 00:00:00 kernel: [    0.991903] PHY 0x73c: 0000081f
Jan 25 00:00:00 kernel: [    0.991904] PHY 0x14: 60692540
Jan 25 00:00:00 kernel: [    0.991908] link count change 20000
Jan 25 00:00:00 kernel: [    0.991909] PHY 0x744: 068fc180
Jan 25 00:00:00 kernel: [    0.991910] 0x2208: 000000ff
Jan 25 00:00:00 kernel: [    0.991911] 0x2400: 00000000
Jan 25 00:00:00 kernel: [    0.991912] 0x2404: 00000000
Jan 25 00:00:00 kernel: [    0.991913] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.991914] 0x240c: 00000000
Jan 25 00:00:00 kernel: [    0.991916] 0x1104: 00000000
Jan 25 00:00:00 kernel: [    0.991917] 0x1108: 01000000
Jan 25 00:00:00 kernel: [    0.991918] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.991919] 0x1134: 01010000
Jan 25 00:00:00 kernel: [    0.991920] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.991921] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.991922] 0x1140: 8003f000
Jan 25 00:00:00 kernel: [    0.991923] PHY 0x3d8: 00000008
Jan 25 00:00:00 kernel: [    0.991924] PHY 0x43d8: 00000008
Jan 25 00:00:00 kernel: [    0.991925] PHY 0x83d8: 00000008
Jan 25 00:00:00 kernel: [    0.991926] PHY 0xc3d8: 00000008
Jan 25 00:00:00 kernel: [    0.991928] PHY 0x1414: 40400040
Jan 25 00:00:00 kernel: [    0.991929] PHY 0x44: 00000000
Jan 25 00:00:00 kernel: [    0.991930] PHY 0x20: 00006080
Jan 25 00:00:00 kernel: [    0.991931] PHY 0x73c: 0000081f
Jan 25 00:00:00 kernel: [    0.991932] PHY 0x14: 60692540
Jan 25 00:00:00 kernel: [    0.992685] state change 2 -> 3
Jan 25 00:00:00 kernel: [    0.992695] pending state change 3 -> 4 @ 1000 ms
Jan 25 00:00:00 kernel: [    0.992700] training pattern change 1000,rate a,cnt 1, pattern 1
Jan 25 00:00:00 kernel: [    0.992747] artosyn_typec_reset_assert 1517 5
Jan 25 00:00:00 kernel: [    0.992759] artosyn_typec_comphy_configure 881 conf 4 2700
Jan 25 00:00:00 kernel: [    0.992766] artosyn_dprx_init_phy 813 get lane info 4 4 1!
Jan 25 00:00:00 kernel: [    0.992771] artosyn_typec_reset_deassert 1546 5
Jan 25 00:00:00 kernel: [    0.992793] 0x2208: 000f0fff
Jan 25 00:00:00 kernel: [    0.992794] 0x2400: 00000000
Jan 25 00:00:00 kernel: [    0.992795] 0x2404: 00000000
Jan 25 00:00:00 kernel: [    0.992796] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.992797] 0x240c: ffffffff
Jan 25 00:00:00 kernel: [    0.992798] 0x1104: 00000000
Jan 25 00:00:00 kernel: [    0.992799] 0x1108: 01000000
Jan 25 00:00:00 kernel: [    0.992800] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.992801] 0x1134: 0401010a
Jan 25 00:00:00 kernel: [    0.992803] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.992804] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.992805] 0x1140: 8008ff00
Jan 25 00:00:00 kernel: [    0.994208] state change 3 -> 6
Jan 25 00:00:00 kernel: [    0.994222] pending state change 6 -> 7 @ 1000 ms
Jan 25 00:00:00 kernel: [    0.994228] training pattern change 1000,rate a,cnt 4, pattern 7
Jan 25 00:00:00 kernel: [    0.994229] 0x2208: 000fffff
Jan 25 00:00:00 kernel: [    0.994230] 0x2400: 00000055
Jan 25 00:00:00 kernel: [    0.994232] 0x2404: 00000055
Jan 25 00:00:00 kernel: [    0.994232] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.994234] 0x240c: fafafafa
Jan 25 00:00:00 kernel: [    0.994235] 0x1104: 0000000f
Jan 25 00:00:00 kernel: [    0.994236] 0x1108: 040f0000
Jan 25 00:00:00 kernel: [    0.994237] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.994238] 0x1134: 0404010a
Jan 25 00:00:00 kernel: [    0.994239] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.994240] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.994241] 0x1140: 8009ff00
Jan 25 00:00:00 kernel: [    0.998924] smartPA_init 
Jan 25 00:00:00 kernel: [    0.998994] smartPA_probe slave addr 52
Jan 25 00:00:00 kernel: [    0.999029] of_get_named_gpio = 79
Jan 25 00:00:00 kernel: [    0.999037] find label: HIGH
Jan 25 00:00:00 kernel: [    0.999042] gpio_direction_output: HIGH
Jan 25 00:00:00 kernel: [    0.999044] find label: smartPA_L
Jan 25 00:00:00 kernel: [    1.020816] hil_mmb_alloc pa:0x33284000 len:204800!
Jan 25 00:00:00 kernel: [    1.021062] hil_mmb_alloc pa:0x332b6000 len:4096!
Jan 25 00:00:00 kernel: [    1.021091] hil_mmb_alloc pa:0x332b7000 len:37273600!
Jan 25 00:00:00 kernel: [    1.021114] hil_mmb_alloc pa:0x35643000 len:2768896!
Jan 25 00:00:00 kernel: [    1.021136] hil_mmb_alloc pa:0x358e7000 len:66600960!
Jan 25 00:00:00 kernel: [    1.021157] hil_mmb_alloc pa:0x3986b000 len:8847360!
Jan 25 00:00:00 kernel: [    1.021178] hil_mmb_alloc pa:0x3a0db000 len:25677824!
Jan 25 00:00:00 kernel: [    1.021199] hil_mmb_alloc pa:0x3b958000 len:81920!
Jan 25 00:00:00 kernel: [    1.027415] training pattern change 1010,rate a,cnt 4, pattern 0
Jan 25 00:00:00 kernel: [    1.027424] 0x2208: 000fffff
Jan 25 00:00:00 kernel: [    1.027425] 0x2400: 00000377
Jan 25 00:00:00 kernel: [    1.027426] 0x2404: 00000377
Jan 25 00:00:00 kernel: [    1.027427] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    1.027429] 0x240c: fafafafa
Jan 25 00:00:00 kernel: [    1.027430] 0x1104: 0000000f
Jan 25 00:00:00 kernel: [    1.027431] 0x1108: 040f0000
Jan 25 00:00:00 kernel: [    1.027432] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    1.027433] 0x1134: 0404010a
Jan 25 00:00:00 kernel: [    1.027434] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    1.027435] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    1.027436] 0x1140: 800cff00
Jan 25 00:00:00 kernel: [    1.027440] state change 6 -> 7
Jan 25 00:00:00 kernel: [    1.027446] training completed 1010(0xa 0xf:4)
Jan 25 00:00:00 kernel: [    1.027448] PHY 0x744: 088fc500
Jan 25 00:00:00 kernel: [    1.027449] 0x2208: 000fffff
Jan 25 00:00:00 kernel: [    1.027450] 0x2400: 00000377
Jan 25 00:00:00 kernel: [    1.027452] 0x2404: 00000377
Jan 25 00:00:00 kernel: [    1.027453] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    1.027454] 0x240c: fafafafa
Jan 25 00:00:00 kernel: [    1.027455] 0x1104: 0000000f
Jan 25 00:00:00 kernel: [    1.027456] 0x1108: 040f0000
Jan 25 00:00:00 kernel: [    1.027457] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    1.027458] 0x1134: 0404010a
Jan 25 00:00:00 kernel: [    1.027459] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    1.027460] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    1.027461] 0x1140: 800cff00
Jan 25 00:00:00 kernel: [    1.027462] PHY 0x3d8: 00000000
Jan 25 00:00:00 kernel: [    1.027463] PHY 0x43d8: 00000010
Jan 25 00:00:00 kernel: [    1.027465] PHY 0x83d8: 00000000
Jan 25 00:00:00 kernel: [    1.027466] PHY 0xc3d8: 00000000
Jan 25 00:00:00 kernel: [    1.027467] PHY 0x1414: 40400040
Jan 25 00:00:00 kernel: [    1.027468] PHY 0x44: 00000000
Jan 25 00:00:00 kernel: [    1.027469] PHY 0x20: 00006080
Jan 25 00:00:00 kernel: [    1.027470] PHY 0x73c: 0000003f
Jan 25 00:00:00 kernel: [    1.027471] PHY 0x14: 60692540
Jan 25 00:00:00 kernel: [    1.027475] Err info 0x0 0x0, 0x0 0x0, 0x0 0x0, 0x0 0x0
Jan 25 00:00:00 kernel: [    1.030995] iqs323 3-0044: iqs323_irq_work:product_number = 1106 
Jan 25 00:00:00 kernel: [    1.034336] iqs323 3-0044: iqs323_irq_work:major_version = 1 
Jan 25 00:00:00 kernel: [    1.037722] iqs323 3-0044: iqs323_irq_work:minor_version = 3 
Jan 25 00:00:00 kernel: [    1.041076] iqs323 3-0044: iqs323_irq_work:iqs323 has been reset
Jan 25 00:00:00 kernel: [    1.091751] iqs323 3-0044: iqs323_config is over : 0
Jan 25 00:00:00 kernel: [    1.108758] using random self ethernet address
Jan 25 00:00:00 kernel: [    1.108766] using random host ethernet address
Jan 25 00:00:00 kernel: [    1.113806] using random self ethernet address
Jan 25 00:00:00 kernel: [    1.113816] using random host ethernet address
Jan 25 00:00:00 kernel: [    1.134556] file system registered
Jan 25 00:00:00 kernel: [    1.138538] artosyn_dprx_irq_thread_1 sdp start
Jan 25 00:00:00 kernel: [    1.139388] Mass Storage Function, version: 2009/09/11
Jan 25 00:00:00 kernel: [    1.139400] LUN: removable file: (no medium)
Jan 25 00:00:00 kernel: [    1.142337] Read iProduct:XREAL One
Jan 25 00:00:00 kernel: [    1.143060] usb0: HOST MAC fc:d2:b6:ad:cc:6d
Jan 25 00:00:00 kernel: [    1.143102] usb0: MAC fc:d2:b6:ad:cc:6a
Jan 25 00:00:00 kernel: [    1.143598] usb1: HOST MAC fc:d2:b6:ad:cc:6c
Jan 25 00:00:00 kernel: [    1.143628] usb1: MAC fc:d2:b6:ad:cc:6b
Jan 25 00:00:00 kernel: [    1.144352] Read iProduct:XREAL One
Jan 25 00:00:00 kernel: [    1.144590] artosyn_kuiper_usb2phy_init 105
Jan 25 00:00:00 kernel: [    1.144621] dwc2 8080000.usb: bound driver configfs-gadget
Jan 25 00:00:00 kernel: [    1.166711] IPv6: ADDRCONF(NETDEV_UP): usb0: link is not ready
Jan 25 00:00:00 kernel: [    1.191210] IPv6: ADDRCONF(NETDEV_UP): usb1: link is not ready
Jan 25 00:00:00 kernel: [    1.198452] ec major : 237, minor : 0^M
Jan 25 00:00:00 kernel: [    1.198497] cdev_add su 
Jan 25 00:00:00 kernel: [    1.198558] class_create su 
Jan 25 00:00:00 kernel: [    1.199094] device_create su 
Jan 25 00:00:00 kernel: [    1.199193] smartPA_probe slave addr 53
Jan 25 00:00:00 kernel: [    1.199240] of_get_named_gpio = 44
Jan 25 00:00:00 kernel: [    1.199251] find label: HIGH
Jan 25 00:00:00 kernel: [    1.199256] gpio_direction_output: HIGH
Jan 25 00:00:00 dhcpd: Internet Systems Consortium DHCP Server 4.4.3-P1
Jan 25 00:00:00 dhcpd: Copyright 2004-2022 Internet Systems Consortium.
Jan 25 00:00:00 dhcpd: All rights reserved.
Jan 25 00:00:00 dhcpd: For info, please visit https://www.isc.org/software/dhcp/
Jan 25 00:00:00 dhcpd: Wrote 0 leases to leases file.
Jan 25 00:00:00 kernel: [    1.199258] find label: smartPA_R
Jan 25 00:00:00 kernel: [    1.328281] dwc2 8080000.usb: new device is high-speed
Jan 25 00:00:00 kernel: [    1.374250] ec major : 237, minor : 1^M
Jan 25 00:00:00 kernel: [    1.374261] cdev_add su 
Jan 25 00:00:00 kernel: [    1.374317] class_create su 
Jan 25 00:00:00 kernel: [    1.374479] device_create su 
Jan 25 00:00:00 kernel: [    1.374600] vsc 28000
Jan 25 00:00:00 kernel: [    1.374606] video change
Jan 25 00:00:00 kernel: [    1.374608] format 0->0
Jan 25 00:00:00 kernel: [    1.374609] depth 0->1
Jan 25 00:00:00 kernel: [    1.374611] colorimetry 0->0
Jan 25 00:00:00 kernel: [    1.374613] hres 0->1920
Jan 25 00:00:00 kernel: [    1.374614] htotal 0->2200
Jan 25 00:00:00 kernel: [    1.374616] vres 0->1080
Jan 25 00:00:00 dhcpd: Listening on LPF/usb1/fc:d2:b6:ad:cc:6b/169.254.1.0/24
Jan 25 00:00:00 dhcpd: Sending on   LPF/usb1/fc:d2:b6:ad:cc:6b/169.254.1.0/24
Jan 25 00:00:00 kernel: [    1.374618] vtotal 0->1125
Jan 25 00:00:00 kernel: [    1.374620] misc0_1 0x0->0x4021
Jan 25 00:00:00 kernel: [    1.374621] mvid 0x0->0x686a
Jan 25 00:00:00 kernel: [    1.374623] nvid 0x0->0x7e90
Jan 25 00:00:00 kernel: [    1.374624] vbid 0x0->0x11
Jan 25 00:00:00 kernel: [    1.374627] pixel clk 222750000
Jan 25 00:00:00 kernel: [    1.374631] color mode 28000
Jan 25 00:00:00 kernel: [    1.406270] Camera plug detect get_cam_plug_detect_config index = 0 
Jan 25 00:00:00 kernel: [    1.406565] get_ts_config index = 0 
Jan 25 00:00:00 kernel: [    1.415156] oled_open idx: 1
Jan 25 00:00:00 kernel: [    1.415257] oled_open idx: 0
Jan 25 00:00:00 kernel: [    1.439250] OLED:ecx343_left initialized successfully with config_index: 2
Jan 25 00:00:00 kernel: [    1.439258] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:00 kernel: [    1.440725] OLED:ecx343_right initialized successfully with config_index: 2
Jan 25 00:00:00 kernel: [    1.440727] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:00 kernel: [    1.440861] Set orbit_h: 0
Jan 25 00:00:00 kernel: [    1.440972] Set orbit_h: 0
Jan 25 00:00:00 kernel: [    1.441070] Set orbit_v: 0
Jan 25 00:00:00 kernel: [    1.441165] Set orbit_v: 0
Jan 25 00:00:00 kernel: [    1.441279] Set white coordinate x:-13, y:-18
Jan 25 00:00:00 kernel: [    1.441312] Set white coordinate x:-13, y:-18
Jan 25 00:00:00 kernel: [    1.448268] dwc2 8080000.usb: new device is high-speed
Jan 25 00:00:00 dhcpd: Listening on LPF/usb0/fc:d2:b6:ad:cc:6a/169.254.2.0/24
Jan 25 00:00:00 dhcpd: Sending on   LPF/usb0/fc:d2:b6:ad:cc:6a/169.254.2.0/24
Jan 25 00:00:00 dhcpd: Sending on   Socket/fallback/fallback-net
Jan 25 00:00:00 dhcpd: Server starting service.
Jan 25 00:00:00 kernel: [    1.465495] ar_vb_open
Jan 25 00:00:01 kernel: [    1.472811] iqs323 3-0044: iqs323_irq_work:ATI error counts: 1
Jan 25 00:00:01 kernel: [    1.472819] iqs323 3-0044: iqs323_irq_work:iqs323 ATI error, reset iqs323
Jan 25 00:00:01 kernel: [    1.479718] get_ts_config index = 0 
Jan 25 00:00:01 kernel: [    1.479727] Enable timestamp irq
Jan 25 00:00:01 kernel: [    1.479740] Invalid ioctl cmd
Jan 25 00:00:01 kernel: [    1.521796] dwc2 8080000.usb: new address 1
Jan 25 00:00:01 kernel: [    1.548559] iqs323 3-0044: gpio_num_57: using irq 113 for Cap Sensor rdy signal detection
Jan 25 00:00:01 kernel: [    1.548567] iqs323 3-0044: iqs323_reset is ready! 
Jan 25 00:00:01 kernel: [    1.562194] binder: 495:634 refcount change on invalid ref 0
Jan 25 00:00:01 kernel: [    1.763421] iqs323 3-0044: iqs323_irq_work:product_number = 1106 
Jan 25 00:00:01 kernel: [    1.763586] iqs323 3-0044: iqs323_irq_work:major_version = 1 
Jan 25 00:00:01 kernel: [    1.763745] iqs323 3-0044: iqs323_irq_work:minor_version = 3 
Jan 25 00:00:01 kernel: [    1.763905] iqs323 3-0044: iqs323_irq_work:iqs323 has been reset
Jan 25 00:00:01 kernel: [    1.768398] iqs323 3-0044: iqs323_config is over : 0
Jan 25 00:00:01 kernel: [    1.799544] configfs-gadget gadget: high-speed config #1: b
Jan 25 00:00:01 kernel: [    1.811807] IPv6: ADDRCONF(NETDEV_CHANGE): usb0: link becomes ready
Jan 25 00:00:01 kernel: [    1.850846] IPv6: ADDRCONF(NETDEV_CHANGE): usb1: link becomes ready
Jan 25 00:00:01 kernel: [    1.859301] random: crng init done
Jan 25 00:00:01 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:01 dhcpd: DHCPOFFER on ************ to fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:01 dhcpd: DHCPREQUEST for ************ (***********) from fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:01 dhcpd: DHCPACK on ************ to fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:01 kernel: [    1.923553] ar_vb_open
Jan 25 00:00:01 kernel: [    1.923694] hil_mmb_alloc pa:0x3bcd6000 len:32768!
Jan 25 00:00:01 kernel: [    1.929047] hil_mmb_alloc pa:0x3bcdf000 len:65536!
Jan 25 00:00:01 kernel: [    1.930059] ar_vb_open
Jan 25 00:00:01 kernel: [    1.935920] start success
Jan 25 00:00:01 kernel: [    1.941458] start success
Jan 25 00:00:01 kernel: [    1.988312] Open is called.
Jan 25 00:00:01 kernel: [    1.989211] Open is called.
Jan 25 00:00:01 kernel: [    1.993824] Open is called.
Jan 25 00:00:01 kernel: [    1.993868] dev_0 request  IRQ success
Jan 25 00:00:01 kernel: [    1.997632] dev_1 request  IRQ success
Jan 25 00:00:01 kernel: [    1.999006] Enabling LVDS lowest power...
Jan 25 00:00:01 kernel: [    1.999014] Write Value: 0x80 to Address: 0xffffff8008edd018
Jan 25 00:00:01 kernel: [    1.999017] Verification successful at Address: 0xffffff8008edd018. Value: 0x80
Jan 25 00:00:01 kernel: [    1.999019] Write Value: 0x1a to Address: 0xffffff8008edd390
Jan 25 00:00:01 kernel: [    1.999021] Verification successful at Address: 0xffffff8008edd390. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.999023] Write Value: 0x1 to Address: 0xffffff8008edd0c0
Jan 25 00:00:01 kernel: [    1.999025] Verification successful at Address: 0xffffff8008edd0c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.999026] Write Value: 0xc0 to Address: 0xffffff8008edd39c
Jan 25 00:00:01 kernel: [    1.999028] Verification successful at Address: 0xffffff8008edd39c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.999030] Write Value: 0xad to Address: 0xffffff8008edd3a0
Jan 25 00:00:01 kernel: [    1.999032] Verification successful at Address: 0xffffff8008edd3a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.999034] Write Value: 0x80 to Address: 0xffffff8008f03418
Jan 25 00:00:01 kernel: [    1.999036] Verification successful at Address: 0xffffff8008f03418. Value: 0x80
Jan 25 00:00:01 kernel: [    1.999038] Write Value: 0x1a to Address: 0xffffff8008f03790
Jan 25 00:00:01 kernel: [    1.999040] Verification successful at Address: 0xffffff8008f03790. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.999042] Write Value: 0x1 to Address: 0xffffff8008f034c0
Jan 25 00:00:01 kernel: [    1.999044] Verification successful at Address: 0xffffff8008f034c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.999045] Write Value: 0xc0 to Address: 0xffffff8008f0379c
Jan 25 00:00:01 kernel: [    1.999047] Verification successful at Address: 0xffffff8008f0379c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.999049] Write Value: 0xad to Address: 0xffffff8008f037a0
Jan 25 00:00:01 kernel: [    1.999051] Verification successful at Address: 0xffffff8008f037a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.999053] Write Value: 0x80 to Address: 0xffffff8008f05018
Jan 25 00:00:01 kernel: [    1.999055] Verification successful at Address: 0xffffff8008f05018. Value: 0x80
Jan 25 00:00:01 kernel: [    1.999057] Write Value: 0x1a to Address: 0xffffff8008f05390
Jan 25 00:00:01 kernel: [    1.999059] Verification successful at Address: 0xffffff8008f05390. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.999060] Write Value: 0x1 to Address: 0xffffff8008f050c0
Jan 25 00:00:01 kernel: [    1.999062] Verification successful at Address: 0xffffff8008f050c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.999064] Write Value: 0xc0 to Address: 0xffffff8008f0539c
Jan 25 00:00:01 kernel: [    1.999066] Verification successful at Address: 0xffffff8008f0539c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.999068] Write Value: 0xad to Address: 0xffffff8008f053a0
Jan 25 00:00:01 kernel: [    1.999070] Verification successful at Address: 0xffffff8008f053a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.999072] Write Value: 0x80 to Address: 0xffffff8008f07418
Jan 25 00:00:01 kernel: [    1.999073] Verification successful at Address: 0xffffff8008f07418. Value: 0x80
Jan 25 00:00:01 kernel: [    1.999075] Write Value: 0x1a to Address: 0xffffff8008f07790
Jan 25 00:00:01 kernel: [    1.999077] Verification successful at Address: 0xffffff8008f07790. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.999079] Write Value: 0x1 to Address: 0xffffff8008f074c0
Jan 25 00:00:01 kernel: [    1.999081] Verification successful at Address: 0xffffff8008f074c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.999083] Write Value: 0xc0 to Address: 0xffffff8008f0779c
Jan 25 00:00:01 kernel: [    1.999085] Verification successful at Address: 0xffffff8008f0779c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.999087] Write Value: 0xad to Address: 0xffffff8008f077a0
Jan 25 00:00:01 kernel: [    1.999089] Verification successful at Address: 0xffffff8008f077a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.999360] ar_vb_open
Jan 25 00:00:01 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:01 dhcpd: DHCPOFFER on ***********0 to fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:01 dhcpd: DHCPREQUEST for ***********0 (***********) from fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:01 dhcpd: DHCPACK on ***********0 to fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:02 kernel: [    2.480854] inv_mpu: imu fifo count mismatch, fifo=1, irq_ts=0
Jan 25 00:00:02 kernel: [    2.521194] inv_mpu: imu fifo count mismatch, fifo=1, irq_ts=0
Jan 25 00:00:02 kernel: [    2.699479] 6921d 2 3
Jan 25 00:00:02 kernel: [    2.851664] read descriptors
Jan 25 00:00:02 kernel: [    2.851681] read strings
Jan 25 00:00:02 kernel: [    2.854497] exFAT-fs (mmcblk0p22[259:14]): trying to mount...
Jan 25 00:00:02 kernel: [    2.855088] exFAT-fs (mmcblk0p22[259:14]): set logical sector size  : 512
Jan 25 00:00:02 kernel: [    2.855096] exFAT-fs (mmcblk0p22[259:14]): (bps : 512, spc : 64, data start : 3072, aligned)
Jan 25 00:00:02 kernel: [    2.855101] exFAT-fs (mmcblk0p22[259:14]): detected volume size     : 2097152 KB (disk : 3829760 KB, part : 2097152 KB)
Jan 25 00:00:02 kernel: [    2.877100] exFAT-fs (mmcblk0p22[259:14]): mounted successfully!
Jan 25 00:00:02 kernel: [    2.888338] Read iProduct:XREAL One
Jan 25 00:00:02 kernel: [    2.890186] Read iProduct:XREAL One
Jan 25 00:00:02 kernel: [    2.890451] artosyn_kuiper_usb2phy_init 105
Jan 25 00:00:02 kernel: [    2.890488] dwc2 8080000.usb: bound driver configfs-gadget
Jan 25 00:00:02 kernel: [    2.894246] inv_mpu: imu fifo count mismatch, fifo=4, irq_ts=1
Jan 25 00:00:02 kernel: [    3.041247] 69000 1 b
Jan 25 00:00:02 kernel: [    3.100039] dwc2 8080000.usb: new device is high-speed
Jan 25 00:00:02 kernel: [    3.145968] 6900b 2 10
Jan 25 00:00:02 kernel: [    3.147345] 6901b 2 10
Jan 25 00:00:02 kernel: [    3.147873] 6902b 2 10
Jan 25 00:00:02 kernel: [    3.148406] 6903b 2 10
Jan 25 00:00:02 kernel: [    3.148938] 6904b 2 10
Jan 25 00:00:02 kernel: [    3.149568] 6905b 2 10
Jan 25 00:00:02 kernel: [    3.150110] 6906b 2 10
Jan 25 00:00:02 kernel: [    3.150695] 6907b 2 10
Jan 25 00:00:02 kernel: [    3.151284] 6908b 2 10
Jan 25 00:00:02 kernel: [    3.151832] 6909b 2 10
Jan 25 00:00:02 kernel: [    3.152358] 690ab 2 10
Jan 25 00:00:02 kernel: [    3.152951] 690bb 2 10
Jan 25 00:00:02 kernel: [    3.153488] 690cb 2 10
Jan 25 00:00:02 kernel: [    3.154894] 690db 2 10
Jan 25 00:00:02 kernel: [    3.155445] 690eb 2 10
Jan 25 00:00:02 kernel: [    3.155950] 690fb 2 10
Jan 25 00:00:02 kernel: [    3.156444] 6910b 2 10
Jan 25 00:00:02 kernel: [    3.156965] 6911b 2 10
Jan 25 00:00:02 kernel: [    3.157480] 6912b 2 10
Jan 25 00:00:02 kernel: [    3.158116] 6913b 2 10
Jan 25 00:00:02 kernel: [    3.158830] 6914b 2 10
Jan 25 00:00:02 kernel: [    3.159361] 6915b 2 10
Jan 25 00:00:02 kernel: [    3.159910] 6916b 2 10
Jan 25 00:00:02 kernel: [    3.160433] 6917b 2 10
Jan 25 00:00:02 kernel: [    3.160959] 6918b 2 10
Jan 25 00:00:02 kernel: [    3.161482] 6919b 2 10
Jan 25 00:00:02 kernel: [    3.162020] 691ab 2 10
Jan 25 00:00:02 kernel: [    3.162547] 691bb 2 10
Jan 25 00:00:02 kernel: [    3.163074] 691cb 2 10
Jan 25 00:00:02 kernel: [    3.163727] 691db 2 10
Jan 25 00:00:02 kernel: [    3.164268] 691eb 2 10
Jan 25 00:00:02 kernel: [    3.164799] 691fb 2 10
Jan 25 00:00:02 kernel: [    3.165324] 6920b 2 10
Jan 25 00:00:02 kernel: [    3.166032] 6921b 2 5
Jan 25 00:00:02 kernel: [    3.214872] 692a0 1 10
Jan 25 00:00:02 kernel: [    3.215240] 692b0 1 10
Jan 25 00:00:02 kernel: [    3.215259] artosyn_dprx_irq_thread_1 692b0
Jan 25 00:00:02 kernel: [    3.215262] artosyn_dprx_hdcp_2_2_calculation 692b0
Jan 25 00:00:02 kernel: [    3.218904] store km: e3 46 66 2a
Jan 25 00:00:02 kernel: [    3.218912] store km: 40 08 47 10
Jan 25 00:00:02 kernel: [    3.218914] store km: 8f b9 88 91
Jan 25 00:00:02 kernel: [    3.218916] store km: 66 f8 b8 cf
Jan 25 00:00:02 kernel: [    3.218926] cal done 10000
Jan 25 00:00:02 kernel: [    3.218931] cal done 10000
Jan 25 00:00:02 kernel: [    3.218934] hw kd: b8 8d c8 5e
Jan 25 00:00:02 kernel: [    3.218936] hw kd: e1 29 24 1e
Jan 25 00:00:02 kernel: [    3.218938] hw kd: f5 b0 24 c4
Jan 25 00:00:02 kernel: [    3.218939] hw kd: db 0e 54 9f
Jan 25 00:00:02 kernel: [    3.218941] hw kd: 28 73 12 49
Jan 25 00:00:02 kernel: [    3.218943] hw kd: 07 ed 16 a0
Jan 25 00:00:02 kernel: [    3.218945] hw kd: f5 da 4f 6c
Jan 25 00:00:02 kernel: [    3.218947] hw kd: 1e be 0a 6d
Jan 25 00:00:02 kernel: [    3.220217] dwc2 8080000.usb: new device is high-speed
Jan 25 00:00:02 kernel: [    3.222754] h: 9d 7a 47 94
Jan 25 00:00:02 kernel: [    3.222762] h: d8 aa ba 25
Jan 25 00:00:02 kernel: [    3.222764] h: fa 88 33 7f
Jan 25 00:00:02 kernel: [    3.222765] h: b6 fc 37 19
Jan 25 00:00:02 kernel: [    3.222767] h: 75 f7 39 a1
Jan 25 00:00:02 kernel: [    3.222769] h: f0 be f5 29
Jan 25 00:00:02 kernel: [    3.222771] h: 6b fd 9a 62
Jan 25 00:00:02 kernel: [    3.222773] h: 1e 6c 3a b0
Jan 25 00:00:02 kernel: [    3.222789] artosyn_typec_event 1631 1!
Jan 25 00:00:02 kernel: [    3.222805] dp_sink_altmode_event 485 1 dp->hpd:1 hpd:1 irq:256
Jan 25 00:00:02 kernel: [    3.222815] dp_sink_altmode_attention_vdm 300
Jan 25 00:00:02 kernel: [    3.222971] artosyn_dprx_dp_pd_event_work 2879 1
Jan 25 00:00:02 kernel: [    3.229015] 69493 2 1
Jan 25 00:00:02 kernel: [    3.229024] rx_status 2
Jan 25 00:00:02 kernel: [    3.229468] 692c0 2 10
Jan 25 00:00:02 kernel: [    3.230040] 692d0 2 10
Jan 25 00:00:02 kernel: [    3.232983] 692f0 1 8
Jan 25 00:00:02 kernel: [    3.232993] L' start
Jan 25 00:00:02 kernel: [    3.233005] artosyn_dprx_irq_thread_1 692f0
Jan 25 00:00:02 kernel: [    3.233008] artosyn_dprx_hdcp_2_2_calculation 692f0
Jan 25 00:00:02 kernel: [    3.233066] L' end
Jan 25 00:00:02 kernel: [    3.233447] 692f8 2 10
Jan 25 00:00:02 kernel: [    3.234042] 69308 2 10
Jan 25 00:00:02 kernel: [    3.237149] 69318 1 10
Jan 25 00:00:02 kernel: [    3.237434] 69328 1 8
Jan 25 00:00:02 kernel: [    3.237450] artosyn_dprx_irq_thread_1 69328
Jan 25 00:00:02 kernel: [    3.237453] artosyn_dprx_hdcp_2_2_calculation 69328
Jan 25 00:00:02 kernel: [    3.237459] cal done 10000
Jan 25 00:00:02 kernel: [    3.237463] hw kd2: 4c 1f e3 2c
Jan 25 00:00:02 kernel: [    3.237465] hw kd2: 5c 59 42 48
Jan 25 00:00:02 kernel: [    3.237467] hw kd2: 71 d7 7d 64
Jan 25 00:00:02 kernel: [    3.237469] hw kd2: f0 fb 83 ef
Jan 25 00:00:02 kernel: [    3.237471] r_n: 69 7c f2 03
Jan 25 00:00:02 kernel: [    3.237473] r_n: 74 22 e2 da
Jan 25 00:00:02 kernel: [    3.237475] tx_r: 6a 85 fd 13
Jan 25 00:00:02 kernel: [    3.237477] tx_r: 3c 7c ed 39
Jan 25 00:00:02 kernel: [    3.237479] rx_r: 4c c1 65 50
Jan 25 00:00:02 kernel: [    3.237482] rx_r: 9b 11 64 72
Jan 25 00:00:02 kernel: [    3.237484] e_dkey_ks: 0b 17 6e 5a
Jan 25 00:00:02 kernel: [    3.237486] e_dkey_ks: ba 6c 44 1c
Jan 25 00:00:02 kernel: [    3.237488] e_dkey_ks: ea 16 a0 53
Jan 25 00:00:02 kernel: [    3.237490] e_dkey_ks: 52 b0 ea 09
Jan 25 00:00:02 kernel: [    3.237492] ks: 47 08 8d 76
Jan 25 00:00:02 kernel: [    3.237494] ks: e6 35 06 54
Jan 25 00:00:02 kernel: [    3.237496] ks: d7 00 b8 67
Jan 25 00:00:02 kernel: [    3.237498] ks: 39 5a 0d 94
Jan 25 00:00:02 kernel: [    3.237507] lc: b5 d8 e9 ab
Jan 25 00:00:02 kernel: [    3.237508] lc: 5f 8a fe ca
Jan 25 00:00:02 kernel: [    3.237511] lc: 38 55 b1 a5
Jan 25 00:00:02 kernel: [    3.237512] lc: 1e c9 bc 0f
Jan 25 00:00:02 kernel: [    3.237514] r_iv: 98 6a ce 80
Jan 25 00:00:02 kernel: [    3.237517] r_iv: 17 06 a5 62
Jan 25 00:00:02 kernel: [    3.237518] artosyn_dprx_hdcp_2_2_start
Jan 25 00:00:02 kernel: [    3.237879] 69494 1 1
Jan 25 00:00:02 kernel: [    3.237882] type 0
Jan 25 00:00:02 kernel: [    3.261003] ------------[ cut here ]------------
Jan 25 00:00:02 kernel: [    3.261027] WARNING: CPU: 1 PID: 869 at ./include/linux/kref.h:46 kobject_get+0x64/0x88
Jan 25 00:00:02 kernel: [    3.261030] Modules linked in: aw883xx_drv(O) iqs323_drv(O) ar_pack(O) camera_plug_detect(O) timestamp_record_exp(O) camera_pwr_ioctl(O) ar_mpp_proc_ctrl(O) ar_sys(O) ar_vb(O) ar_osal(O) ar_mpp_drv(O)
Jan 25 00:00:02 kernel: [    3.261053] 
Jan 25 00:00:02 kernel: [    3.261060] CPU: 1 PID: 869 Comm: command_service Tainted: G           O    4.9.38 #3
Jan 25 00:00:02 kernel: [    3.261062] Hardware name: Artosyn, Kuiper Development Board (DT)
Jan 25 00:00:02 kernel: [    3.261065] task: ffffffc0308d8000 task.stack: ffffffc03073c000
Jan 25 00:00:02 kernel: [    3.261069] PC is at kobject_get+0x64/0x88
Jan 25 00:00:02 kernel: [    3.261075] LR is at cdev_get+0x30/0x68
Jan 25 00:00:02 kernel: [    3.261077] pc : [<ffffff800831c53c>] lr : [<ffffff800818e400>] pstate: 60000145
Jan 25 00:00:02 kernel: [    3.261079] sp : ffffffc03073fb00
Jan 25 00:00:02 kernel: [    3.261080] x29: ffffffc03073fb00 x28: ffffff800818def0 
Jan 25 00:00:02 kernel: [    3.261085] x27: 0000000000000000 x26: ffffffc030065480 
Jan 25 00:00:02 kernel: [    3.261088] x25: 00000000000000ee x24: ffffffc03073fbfc 
Jan 25 00:00:02 kernel: [    3.261092] x23: 000000000ee00000 x22: ffffffc031009800 
Jan 25 00:00:02 kernel: [    3.261095] x21: 0000000000000000 x20: 0000000000000000 
Jan 25 00:00:02 kernel: [    3.261099] x19: ffffffc030065480 x18: 0000007fdf6761fd 
Jan 25 00:00:02 kernel: [    3.261103] x17: 0000007f9ad87c90 x16: ffffff80081883a0 
Jan 25 00:00:02 kernel: [    3.261107] x15: 000000000000000a x14: 0000000000000001 
Jan 25 00:00:02 kernel: [    3.261110] x13: 0000000000000000 x12: 0000000000000000 
Jan 25 00:00:02 kernel: [    3.261114] x11: 0000000000000000 x10: d0d0d0e0b7b4b9b8 
Jan 25 00:00:02 kernel: [    3.261117] x9 : 0000000000000000 x8 : ffffffc031180aa0 
Jan 25 00:00:02 kernel: [    3.261121] x7 : 0000000000000019 x6 : 0000000000000000 
Jan 25 00:00:02 kernel: [    3.261124] x5 : ffffffc031180a80 x4 : 0000000000001000 
Jan 25 00:00:02 kernel: [    3.261128] x3 : 0000000000000000 x2 : ffffffc0300654b8 
Jan 25 00:00:02 kernel: [    3.261132] x1 : 0000000000000001 x0 : ffffff8008c27000 
Jan 25 00:00:02 kernel: [    3.261135] 
Jan 25 00:00:02 kernel: [    3.261137] ---[ end trace 41c0d0f2e99af5db ]---
Jan 25 00:00:02 kernel: [    3.261140] Call trace:
Jan 25 00:00:02 kernel: [    3.261145] Exception stack(0xffffffc03073f930 to 0xffffffc03073fa60)
Jan 25 00:00:02 kernel: [    3.261151] f920:                                   ffffffc030065480 0000008000000000
Jan 25 00:00:02 kernel: [    3.261158] f940: ffffffc03073fb00 ffffff800831c53c ffffffc03073f960 ffffff800820d764
Jan 25 00:00:02 kernel: [    3.261164] f960: ffffffc03073f990 ffffff8008162688 00000000000000d7 ffffffc03073fa48
Jan 25 00:00:02 kernel: [    3.261170] f980: ffffffc03073f990 ffffff800814d750 ffffffc03073f9a0 ffffff8008345114
Jan 25 00:00:02 kernel: [    3.261175] f9a0: ffffffc03073f9e0 ffffff80080adca4 0000000000000003 ffffffc03067e300
Jan 25 00:00:02 kernel: [    3.261179] f9c0: 0000000000000000 0000000000000001 ffffff8008c27000 0000000000000001
Jan 25 00:00:02 kernel: [    3.261182] f9e0: ffffffc0300654b8 0000000000000000 0000000000001000 ffffffc031180a80
Jan 25 00:00:02 kernel: [    3.261186] fa00: 0000000000000000 0000000000000019 ffffffc031180aa0 0000000000000000
Jan 25 00:00:02 kernel: [    3.261189] fa20: d0d0d0e0b7b4b9b8 0000000000000000 0000000000000000 0000000000000000
Jan 25 00:00:02 kernel: [    3.261192] fa40: 0000000000000001 000000000000000a ffffff80081883a0 0000007f9ad87c90
Jan 25 00:00:02 kernel: [    3.261197] [<ffffff800831c53c>] kobject_get+0x64/0x88
Jan 25 00:00:02 kernel: [    3.261200] [<ffffff800818e400>] cdev_get+0x30/0x68
Jan 25 00:00:02 kernel: [    3.261203] [<ffffff800818e448>] exact_lock+0x10/0x20
Jan 25 00:00:02 kernel: [    3.261212] [<ffffff80083b2d40>] kobj_lookup+0xc8/0x158
Jan 25 00:00:02 kernel: [    3.261216] [<ffffff800818e830>] chrdev_open+0x110/0x1a0
Jan 25 00:00:02 kernel: [    3.261222] [<ffffff8008186e30>] do_dentry_open.isra.1+0x178/0x318
Jan 25 00:00:02 kernel: [    3.261226] [<ffffff8008187e7c>] vfs_open+0x44/0x70
Jan 25 00:00:02 kernel: [    3.261233] [<ffffff80081984fc>] path_openat+0x4fc/0x1030
Jan 25 00:00:02 kernel: [    3.261237] [<ffffff8008199fe8>] do_filp_open+0x60/0xd8
Jan 25 00:00:02 kernel: [    3.261241] [<ffffff80081882d8>] do_sys_open+0x170/0x210
Jan 25 00:00:02 kernel: [    3.261244] [<ffffff80081883b0>] SyS_openat+0x10/0x18
Jan 25 00:00:02 kernel: [    3.261250] [<ffffff80080a2730>] el0_svc_naked+0x24/0x28
Jan 25 00:00:02 kernel: [    3.289992] dwc2 8080000.usb: new address 2
Jan 25 00:00:03 kernel: [    3.591481] configfs-gadget gadget: high-speed config #1: b
Jan 25 00:00:03 dhcpd: reuse_lease: lease age 2 (secs) under 25% threshold, reply with unaltered, existing lease for ************
Jan 25 00:00:03 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:03 dhcpd: DHCPOFFER on ************ to fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:03 dhcpd: reuse_lease: lease age 2 (secs) under 25% threshold, reply with unaltered, existing lease for ************
Jan 25 00:00:03 dhcpd: DHCPREQUEST for ************ (***********) from fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:03 dhcpd: DHCPACK on ************ to fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:03 dhcpd: reuse_lease: lease age 2 (secs) under 25% threshold, reply with unaltered, existing lease for ***********0
Jan 25 00:00:03 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:03 dhcpd: DHCPOFFER on ***********0 to fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:03 dhcpd: reuse_lease: lease age 2 (secs) under 25% threshold, reply with unaltered, existing lease for ***********0
Jan 25 00:00:03 dhcpd: DHCPREQUEST for ***********0 (***********) from fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:03 dhcpd: DHCPACK on ***********0 to fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:04 kernel: [    4.536955] mmc5603 5-0030: [MMC5603] Mmc5603 data is not ready,status=16
Jan 25 00:00:04 kernel: [    4.897112] inv_mpu: imu fifo count mismatch, fifo=1, irq_ts=6
Jan 25 00:00:04 kernel: [    4.897942] inv_mpu: imu fifo count mismatch, fifo=6, irq_ts=7
Jan 25 00:00:04 kernel: [    4.897984] inv_mpu: drop this invalid imu irq timestamp=4888656418, last_irq_ts=4888263668 time_diff=392750
Jan 25 00:00:05 kernel: [    5.506580] dev_0 SUB_CLEAR success
Jan 25 00:00:05 kernel: [    5.508751] dev_0 free IRQ success
Jan 25 00:00:05 kernel: [    5.509707] dev_1 SUB_CLEAR success
Jan 25 00:00:05 kernel: [    5.510032] dev_1 free IRQ success
Jan 25 00:00:05 kernel: [    5.531276] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:05 kernel: [    5.531286] mmb(0x3BD38000) not found!
Jan 25 00:00:05 kernel: [    5.531358] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:05 kernel: [    5.531360] mmb(0x3BD39000) not found!
Jan 25 00:00:05 kernel: [    5.531405] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:05 kernel: [    5.531407] mmb(0x3BD3A000) not found!
Jan 25 00:00:05 kernel: [    5.531452] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:05 kernel: [    5.531453] mmb(0x3BD3B000) not found!
Jan 25 00:00:05 kernel: [    5.531548] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:05 kernel: [    5.531550] mmb(0x3BD3C000) not found!
Jan 25 00:00:05 kernel: [    5.531595] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:05 kernel: [    5.531597] mmb(0x3BD3D000) not found!
Jan 25 00:00:05 kernel: [    5.531633] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:05 kernel: [    5.531635] mmb(0x3BD3E000) not found!
Jan 25 00:00:05 kernel: [    5.531670] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:05 kernel: [    5.531671] mmb(0x3BD3F000) not found!
Jan 25 00:00:05 kernel: [    5.533322] OLED:ecx343_left initialized successfully with config_index: 2
Jan 25 00:00:05 kernel: [    5.533329] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:05 kernel: [    5.534677] OLED:ecx343_right initialized successfully with config_index: 2
Jan 25 00:00:05 kernel: [    5.534686] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:05 kernel: [    5.534889] Set orbit_h: 0
Jan 25 00:00:05 kernel: [    5.534982] Set orbit_h: 0
Jan 25 00:00:05 kernel: [    5.535069] Set orbit_v: 0
Jan 25 00:00:05 kernel: [    5.535147] Set orbit_v: 0
Jan 25 00:00:05 kernel: [    5.739518] dev_0 request  IRQ success
Jan 25 00:00:05 kernel: [    5.742516] dev_1 request  IRQ success
Jan 25 00:00:05 kernel: [    5.743879] Enabling LVDS lowest power...
Jan 25 00:00:05 kernel: [    5.743889] Write Value: 0x80 to Address: 0xffffff8008edd018
Jan 25 00:00:05 kernel: [    5.743891] Verification successful at Address: 0xffffff8008edd018. Value: 0x80
Jan 25 00:00:05 kernel: [    5.743893] Write Value: 0x1a to Address: 0xffffff8008edd390
Jan 25 00:00:05 kernel: [    5.743895] Verification successful at Address: 0xffffff8008edd390. Value: 0x1a
Jan 25 00:00:05 kernel: [    5.743896] Write Value: 0x1 to Address: 0xffffff8008edd0c0
Jan 25 00:00:05 kernel: [    5.743898] Verification successful at Address: 0xffffff8008edd0c0. Value: 0x1
Jan 25 00:00:05 kernel: [    5.743900] Write Value: 0xc0 to Address: 0xffffff8008edd39c
Jan 25 00:00:05 kernel: [    5.743901] Verification successful at Address: 0xffffff8008edd39c. Value: 0xc0
Jan 25 00:00:05 kernel: [    5.743903] Write Value: 0xad to Address: 0xffffff8008edd3a0
Jan 25 00:00:05 kernel: [    5.743905] Verification successful at Address: 0xffffff8008edd3a0. Value: 0xad
Jan 25 00:00:05 kernel: [    5.743907] Write Value: 0x80 to Address: 0xffffff8008f03418
Jan 25 00:00:05 kernel: [    5.743908] Verification successful at Address: 0xffffff8008f03418. Value: 0x80
Jan 25 00:00:05 kernel: [    5.743910] Write Value: 0x1a to Address: 0xffffff8008f03790
Jan 25 00:00:05 kernel: [    5.743912] Verification successful at Address: 0xffffff8008f03790. Value: 0x1a
Jan 25 00:00:05 kernel: [    5.743914] Write Value: 0x1 to Address: 0xffffff8008f034c0
Jan 25 00:00:05 kernel: [    5.743915] Verification successful at Address: 0xffffff8008f034c0. Value: 0x1
Jan 25 00:00:05 kernel: [    5.743917] Write Value: 0xc0 to Address: 0xffffff8008f0379c
Jan 25 00:00:05 kernel: [    5.743918] Verification successful at Address: 0xffffff8008f0379c. Value: 0xc0
Jan 25 00:00:05 kernel: [    5.743920] Write Value: 0xad to Address: 0xffffff8008f037a0
Jan 25 00:00:05 kernel: [    5.743922] Verification successful at Address: 0xffffff8008f037a0. Value: 0xad
Jan 25 00:00:05 kernel: [    5.743923] Write Value: 0x80 to Address: 0xffffff8008f05018
Jan 25 00:00:05 kernel: [    5.743925] Verification successful at Address: 0xffffff8008f05018. Value: 0x80
Jan 25 00:00:05 kernel: [    5.743926] Write Value: 0x1a to Address: 0xffffff8008f05390
Jan 25 00:00:05 kernel: [    5.743928] Verification successful at Address: 0xffffff8008f05390. Value: 0x1a
Jan 25 00:00:05 kernel: [    5.743929] Write Value: 0x1 to Address: 0xffffff8008f050c0
Jan 25 00:00:05 kernel: [    5.743931] Verification successful at Address: 0xffffff8008f050c0. Value: 0x1
Jan 25 00:00:05 kernel: [    5.743933] Write Value: 0xc0 to Address: 0xffffff8008f0539c
Jan 25 00:00:05 kernel: [    5.743934] Verification successful at Address: 0xffffff8008f0539c. Value: 0xc0
Jan 25 00:00:05 kernel: [    5.743936] Write Value: 0xad to Address: 0xffffff8008f053a0
Jan 25 00:00:05 kernel: [    5.743937] Verification successful at Address: 0xffffff8008f053a0. Value: 0xad
Jan 25 00:00:05 kernel: [    5.743939] Write Value: 0x80 to Address: 0xffffff8008f07418
Jan 25 00:00:05 kernel: [    5.743941] Verification successful at Address: 0xffffff8008f07418. Value: 0x80
Jan 25 00:00:05 kernel: [    5.743942] Write Value: 0x1a to Address: 0xffffff8008f07790
Jan 25 00:00:05 kernel: [    5.743944] Verification successful at Address: 0xffffff8008f07790. Value: 0x1a
Jan 25 00:00:05 kernel: [    5.743945] Write Value: 0x1 to Address: 0xffffff8008f074c0
Jan 25 00:00:05 kernel: [    5.743947] Verification successful at Address: 0xffffff8008f074c0. Value: 0x1
Jan 25 00:00:05 kernel: [    5.743948] Write Value: 0xc0 to Address: 0xffffff8008f0779c
Jan 25 00:00:05 kernel: [    5.743950] Verification successful at Address: 0xffffff8008f0779c. Value: 0xc0
Jan 25 00:00:05 kernel: [    5.743952] Write Value: 0xad to Address: 0xffffff8008f077a0
Jan 25 00:00:05 kernel: [    5.743953] Verification successful at Address: 0xffffff8008f077a0. Value: 0xad
Jan 25 00:00:05 kernel: [    5.746418] Get switch state: 0
Jan 25 00:00:05 kernel: [    5.746497] Get switch state: 0
Jan 25 00:00:05 kernel: [    5.770223] Brightness register values: 00 0f
Jan 25 00:00:05 kernel: [    5.770232] Get brightness: 15
Jan 25 00:00:05 kernel: [    5.770311] Brightness register values: 00 00
Jan 25 00:00:05 kernel: [    5.770312] Get brightness: 0
Jan 25 00:00:05 kernel: [    5.770904] Get switch state: 1
Jan 25 00:00:05 kernel: [    5.770979] Get switch state: 1
Jan 25 00:00:05 kernel: [    5.771505] Set white coordinate x:-17, y:-22
Jan 25 00:00:05 kernel: [    5.771909] Set white coordinate x:-21, y:-23
Jan 25 00:00:05 kernel: [    5.793547] Brightness register values: 00 00
Jan 25 00:00:05 kernel: [    5.793563] Get brightness: 0
Jan 25 00:00:05 kernel: [    5.793666] Brightness register values: 00 00
Jan 25 00:00:05 kernel: [    5.793667] Get brightness: 0
Jan 25 00:00:05 kernel: [    5.833250] Brightness register values: 00 2c
Jan 25 00:00:05 kernel: [    5.833260] Get brightness: 44
Jan 25 00:00:05 kernel: [    5.833334] Brightness register values: 00 2c
Jan 25 00:00:05 kernel: [    5.833335] Get brightness: 44
Jan 25 00:00:05 kernel: [    5.873532] Brightness register values: 00 58
Jan 25 00:00:05 kernel: [    5.873550] Get brightness: 88
Jan 25 00:00:05 kernel: [    5.873627] Brightness register values: 00 58
Jan 25 00:00:05 kernel: [    5.873629] Get brightness: 88
Jan 25 00:00:05 kernel: [    5.913351] Brightness register values: 00 84
Jan 25 00:00:05 kernel: [    5.913365] Get brightness: 132
Jan 25 00:00:05 kernel: [    5.913447] Brightness register values: 00 84
Jan 25 00:00:05 kernel: [    5.913448] Get brightness: 132
Jan 25 00:00:05 kernel: [    5.953268] Brightness register values: 00 b0
Jan 25 00:00:05 kernel: [    5.953278] Get brightness: 176
Jan 25 00:00:05 kernel: [    5.953358] Brightness register values: 00 b0
Jan 25 00:00:05 kernel: [    5.953359] Get brightness: 176
Jan 25 00:00:05 kernel: [    5.993491] Brightness register values: 00 dc
Jan 25 00:00:05 kernel: [    5.993501] Get brightness: 220
Jan 25 00:00:05 kernel: [    5.993581] Brightness register values: 00 dc
Jan 25 00:00:05 kernel: [    5.993582] Get brightness: 220
Jan 25 00:00:05 kernel: [    6.033244] Brightness register values: 01 08
Jan 25 00:00:05 kernel: [    6.033254] Get brightness: 264
Jan 25 00:00:05 kernel: [    6.033329] Brightness register values: 01 08
Jan 25 00:00:05 kernel: [    6.033330] Get brightness: 264
Jan 25 00:00:05 kernel: [    6.073223] Brightness register values: 01 34
Jan 25 00:00:05 kernel: [    6.073230] Get brightness: 308
Jan 25 00:00:05 kernel: [    6.073310] Brightness register values: 01 34
Jan 25 00:00:05 kernel: [    6.073311] Get brightness: 308
Jan 25 00:00:05 kernel: [    6.113395] Brightness register values: 01 60
Jan 25 00:00:05 kernel: [    6.113405] Get brightness: 352
Jan 25 00:00:05 kernel: [    6.113482] Brightness register values: 01 60
Jan 25 00:00:05 kernel: [    6.113483] Get brightness: 352
Jan 25 00:00:05 kernel: [    6.153270] Brightness register values: 01 8c
Jan 25 00:00:05 kernel: [    6.153280] Get brightness: 396
Jan 25 00:00:05 kernel: [    6.153361] Brightness register values: 01 8c
Jan 25 00:00:05 kernel: [    6.153362] Get brightness: 396
Jan 25 00:00:07 kernel: [    8.365729] Get switch state: 1
Jan 25 00:00:07 kernel: [    8.365824] Get switch state: 1
